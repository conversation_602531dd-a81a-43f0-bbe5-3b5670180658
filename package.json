{"name": "schola-api-ts", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"prebuild": "npm run --silent lint", "build": "export NODE_OPTIONS=--max_old_space_size=3072 && tsc", "postbuild": "npm run --silent success", "success": "echo \"\\033[32mBuild successfull\"", "start": "node --max-old-space-size=4096 $(node -e \"(process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'staging') ? console.log('-r dotenv/config') : console.log('-r newrelic -r dotenv/config')\") dist/app.js", "startlocal": "node -r dotenv/config dist/app.js", "dev": "nodemon -r dotenv/config -r tsconfig-paths/register app.ts", "watch": "nodemon --watch . --ext ts,html --exec \"npm run build && npm run startlocal\"", "test": "node --max-old-space-size=8192 --expose-gc ./node_modules/.bin/jest --silent --onlyChanged --logHeapUsage --detectOpenHandles --setupFiles dotenv/config", "alltests": "jest --max-old-space-size=8192 --silent --detect<PERSON><PERSON><PERSON><PERSON><PERSON>", "newman": "newman run -e newman-tests/dev-environment.json", "prepare": "husky install", "lint": "npx eslint .", "lintfix": "npx eslint . --fix"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@commitlint/cli": "^16.2.1", "@commitlint/config-conventional": "^16.2.1", "@types/archiver": "^5.3.2", "@types/auth0": "^2.35.0", "@types/aws4": "^1.11.2", "@types/cheerio": "^0.22.31", "@types/connect-timeout": "^0.0.37", "@types/cors": "^2.8.12", "@types/elasticsearch": "^5.0.40", "@types/express": "^4.17.13", "@types/facebook-nodejs-business-sdk": "^15.0.2", "@types/geoip-lite": "^1.4.1", "@types/imap": "^0.8.35", "@types/imapflow": "^1.0.13", "@types/jest": "^29.5.0", "@types/jsforce": "^1.11.0", "@types/jsonwebtoken": "^8.5.8", "@types/lodash": "^4.14.182", "@types/mailparser": "^3.4.0", "@types/multer": "^1.4.7", "@types/newman": "^5.3.0", "@types/node": "^18.11.18", "@types/pdfmake": "^0.2.2", "@types/pg": "^8.6.5", "@types/request": "^2.48.8", "@types/sinon": "^10.0.11", "@types/swagger-jsdoc": "^6.0.1", "@types/swagger-ui-express": "^4.1.3", "@types/traverse": "^0.6.33", "@types/uuid": "^8.3.4", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.17.0", "eslint": "^8.12.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-check-file": "^2.5.0", "eslint-plugin-import": "^2.25.4", "husky": "^7.0.4", "jest": "^29.3.1", "newman": "^5.3.2", "nodemon": "^3.1.9", "sinon": "^13.0.1", "ts-jest": "^29.0.5", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.2", "typescript": "^4.6.3"}, "dependencies": {"@aws-sdk/client-s3": "^3.88.0", "@aws-sdk/credential-provider-node": "^3.264.0", "@aws-sdk/credential-providers": "^3.264.0", "@aws-sdk/types": "^3.257.0", "@google-cloud/local-auth": "^2.1.0", "@opensearch-project/opensearch": "^2.1.0", "@types/ua-parser-js": "^0.7.36", "archiver": "^5.3.1", "auth0": "^2.42.0", "axios": "^0.27.2", "cheerio": "^1.0.0-rc.12", "connect-timeout": "^1.9.0", "cors": "^2.8.5", "cross-fetch": "^3.1.5", "csv-parse": "^5.3.3", "csv-stringify": "^6.3.0", "dotenv": "^16.0.0", "express": "^4.17.3", "ext-name": "^5.0.0", "facebook-nodejs-business-sdk": "^23.0.0", "form-data": "^4.0.0", "geoip-lite": "^1.4.6", "googleapis": "^105.0.0", "imapflow": "^1.0.136", "jimp": "^0.17.0", "jsforce": "^1.11.0", "jsonschema": "^1.4.0", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "mailgun.js": "^9.0.1", "mailparser": "^3.5.0", "module-alias": "^2.2.2", "moment": "^2.29.1", "multer": "^1.4.4", "nanoid": "^3.3.4", "pdfmake": "^0.2.7", "pg": "^8.7.3", "plaid": "^8.0.0", "stripe": "^12.14.0", "swagger-jsdoc": "^6.1.0", "swagger-ui-express": "^4.3.0", "traverse": "^0.6.7", "twilio": "^3.77.3", "ua-parser-js": "^1.0.32", "uuid": "^8.3.2", "winston": "^3.9.0"}, "nodemonConfig": {"ignore": ["**/test/**"]}, "_moduleAliases": {"@config": "dist/src/config", "@constants": "dist/src/constants", "@controllers": "dist/src/controllers", "@email-templates": "dist/src/email-templates", "@errors": "dist/src/error-handling", "@errorClasses": "dist/src/error-handling/api-errors", "@interfaces": "dist/src/interfaces", "@marketing-campaign-templates": "dist/src/marketing-campaign-templates", "@middleware": "dist/src/middleware", "@queries": "dist/src/queries", "@routes": "dist/src/routes", "@schemas": "dist/src/schemas", "@utils": "dist/src/utils", "@connectors": "dist/src/connectors"}}