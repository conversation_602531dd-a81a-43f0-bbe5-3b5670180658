import { useRef, useEffect } from 'react';

export const useClickOutside = (callback) => {
  const domRef = useRef();

  useEffect(() => {
    const handler = (event) => {
      if (domRef.current && !domRef.current.contains(event.target)) {
        callback();
      }
    };

    document.addEventListener('mousedown', handler);

    return () => {
      document.removeEventListener('mousedown', handler);
    };
  }, [callback]);

  return domRef;
};
