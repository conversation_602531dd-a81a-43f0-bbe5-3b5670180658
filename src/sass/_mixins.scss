@use 'sass:map';
@use 'sass:math';
@import 'color-palette';

@mixin theme-aware($key, $color) {
  @each $theme-name, $theme-color in $themes {
    .theme-#{$theme-name} & {
      #{$key}: map-get(map-get($themes, $theme-name), $color) !important;
    }
  }
}

// mixin receibe rule property and valud like pixel for return property like rem
@mixin toRem($property, $value) {
  $remvalue: math.div($value, 16);
  #{$property}: $remvalue + rem;
}

@mixin panel {
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
}
@mixin panel6px {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 11px 0 $dark-blue-25;
  overflow: hidden;
}

@mixin planHeader() {
  text-align: center;
  .plan-name {
    font-size: 20px;
    margin-top: 47px;
    margin-bottom: 26px;
    font-weight: 700;
  }
  .plan-price {
    font-size: 48px;
    margin-bottom: 8px;
    @include theme-aware('color', 'primary');
    font-weight: 600;
  }
  .plan-caption {
    font-size: 14px;
    margin-bottom: 20px;
    @include theme-aware('color', 'dark-blue-50');
  }
  .plan-caption-annually {
    @include theme-aware('color', 'dark-blue-75');
    font-size: 12px;
    margin-bottom: 24px;
    font-weight: 400;
  }
}
@mixin lead_vignette($color) {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 2px;
  background-color: $color;
}
@mixin option_type {
  display: flex;
  gap: 1rem;
  align-items: center;
  padding: 1rem;
  width: 100%;
  background-color: #ffffff;
  cursor: pointer;
}

@mixin antd-icon-bg-border-radius($font-size, $border-radius, $bg-color) {
  margin-right: 10px;
  font-size: $font-size;
  color: $primary;
  padding: 0.5rem;
  background-color: $bg-color;
  border-radius: $border-radius;
}

//Override colors from schola-components (new colors) temps
.button-small-primary,
.button-medium-primary,
.button-large-primary {
  background-color: $primary;
}

p.font-14-dark-blue-50 {
  font-size: 14px !important;
  color: $dark-blue-50;
  margin-bottom: 0;
}
p.font-14-dark-blue-75 {
  font-size: 14px !important;
  color: $dark-blue-75;
  margin-bottom: 0;
}
.switch .slider,
.switch input:checked + .slider {
  background: $dark-blue-100;
}

// utils classes

.w-100 {
  width: 100% !important;
}
.main-icon-square {
  @include antd-icon-bg-border-radius(24px, 0.3rem, #e6f0f5);
}
.main-icon-round {
  @include antd-icon-bg-border-radius(24px, 50%, #e6f0f5);
}
.border-lightgray {
  border: 1px solid #e5edd5;
}
.border-radius-8 {
  border-radius: 0.5rem;
}
.border-bottom-gray {
  border-bottom: 1px solid #e5edd5;
}
.icon-bg-round-16 {
  @include antd-icon-bg-border-radius(16px, 50%, #e6f0f5);
}
.align-self-center {
  align-self: center;
}
.box {
  border: 1px solid rgb(226, 232, 240);
  background: rgb(255, 255, 255);
  padding: 16px;
  border-radius: 8px;
  margin: 1rem 0;
  position: relative;
}
.box-shadow-hover {
  &:hover {
    box-shadow: 0 2px 11px 0 rgba(0, 0, 0, 0.08), 0 3px 12px 0 rgba(0, 0, 0, 0);
  }
}
.icon-16 {
  color: inherit;
  font-size: 1rem;
}
.icon-24 {
  color: inherit;
  font-size: 1.5rem;
}
