import {
  SET_VALUE,
  SET_ASSIGNED_MODE,
  OPEN_SCHOOL_DETAILS,
  CLOSE_SCHOOL_DETAILS,
  OPEN_TASK_LEAD,
  CLOSE_TASK_LEAD,
  TOGGLE_ADVANCED_FILTER,
  TOGGLE_NEW_LEAD,
  SET_STATUS_FILTER,
  SET_FILTER_STATUS,
  SET_ADVANCED_FILTERS,
  SET_SELECTED_LEAD,
  SET_SELECTED_LEADS,
  CLOSE_UPDATE_LEAD,
  OPEN_UPDATE_LEAD,
  SELECT_ALL_LEADS,
  C<PERSON>AR_SELECTED_LEADS,
  SET_USER,
  CLEAR_SELECTED_LEAD,
  SET_TASKS,
  SET_NOTIFICATIONS,
  SET_UNREAD_NOTIFICATIONS,
  C<PERSON>AR_ADVANCED_FILTERS,
  TOGGLE_MESSAGE,
  SET_MESSAGE_TYPE,
} from './spot-actions';

const initialState = {
  isSPOTadmin: false,
  roles: [],
  userStatus: '',
  cloudtalkId: null,
  queueTag: '',
  schoolsOwned: [],
  messages: {},
  isAssignedMode: false,
  schoolId: '',
  isSchoolDetailsOpen: false,
  selectedSchool: null,
  isMessageOpen: false,
  isTaskLeadOpen: false,
  isUpdateLeadOpen: false,
  selectedLead: null,
  isAdvancedFilterOpen: false,
  isNewLeadOpen: false,
  statusFilter: 'new', // Default status filter
  filterValue: '',
  selectedLeads: [],
  messageType: 'sms',
  advancedFilters: {
    createdAt_from: '',
    createdAt_to: '',
    grades: [],
    search_notes: '',
    year: null,
    language: null,
    lead_source_id: null,
    lead_status_id: null,
  },
  tasks: {
    pending: [],
    completed: [],
  },
  notifications: [],
  unreadNotifications: [],
};

export const spotReducer = (state = initialState, action) => {
  switch (action.type) {
    case SET_VALUE:
      return {
        ...state,
        [action.payload.key]: action.payload.value,
      };

    case SET_ASSIGNED_MODE:
      return {
        ...state,
        isAssignedMode: !state.isAssignedMode,
      };

    case OPEN_SCHOOL_DETAILS:
      return {
        ...state,
        isSchoolDetailsOpen: true,
        selectedSchool: action.payload,
      };

    case CLOSE_SCHOOL_DETAILS:
      return {
        ...state,
        isSchoolDetailsOpen: false,
        selectedSchool: null,
      };

    case OPEN_TASK_LEAD:
      return {
        ...state,
        isTaskLeadOpen: true,
      };

    case CLOSE_TASK_LEAD:
      return {
        ...state,
        isTaskLeadOpen: false,
        selectedLead: null,
      };

    case OPEN_UPDATE_LEAD:
      return {
        ...state,
        isUpdateLeadOpen: true,
        // selectedLead: action.payload,
      };

    case CLOSE_UPDATE_LEAD:
      return {
        ...state,
        isUpdateLeadOpen: false,
        // selectedLead: null,
      };

    case TOGGLE_ADVANCED_FILTER:
      return {
        ...state,
        isAdvancedFilterOpen: !state.isAdvancedFilterOpen,
      };

    case TOGGLE_NEW_LEAD:
      return {
        ...state,
        isNewLeadOpen: !state.isNewLeadOpen,
      };

    case SET_STATUS_FILTER:
      return {
        ...state,
        statusFilter: action.payload,
      };

    case SET_FILTER_STATUS:
      return {
        ...state,
        filterValue: action.payload,
      };

    case SET_ADVANCED_FILTERS:
      return {
        ...state,
        advancedFilters: action.payload,
      };

    case CLEAR_ADVANCED_FILTERS:
      return {
        ...state,
        advancedFilters: {
          createdAt_from: '',
          createdAt_to: '',
          grades: [],
          search_notes: '',
          year: null,
          language: null,
          lead_source_id: null,
          lead_status_id: null,
          custom_field_1: null,
          custom_field_2: null,
          custom_field_3: null,
          custom_field_4: null,
          custom_field_5: null,
        },
      };

    case SET_SELECTED_LEADS:
      const isSelected = state.selectedLeads.includes(action.payload);

      const currentValue = state.selectedLeads;

      return {
        ...state,
        selectedLeads: isSelected
          ? currentValue.filter((item) => item !== action.payload)
          : [...currentValue, action.payload],
      };

    case SELECT_ALL_LEADS:
      return {
        ...state,
        selectedLeads: action.payload,
      };

    case CLEAR_SELECTED_LEADS:
      return {
        ...state,
        selectedLeads: [],
      };

    case SET_USER:
      return {
        ...state,
        userStatus: action.payload.status,
        queueTag: action.payload.tag,
        roles: action.payload.role,
        isSPOTadmin: action.payload.isSPOTadmin,
        cloudtalkId: action.payload.cloudtalkId,
      };

    case SET_SELECTED_LEAD:
      return {
        ...state,
        selectedLead: action.payload,
      };

    case CLEAR_SELECTED_LEAD:
      return {
        ...state,
        selectedLead: null,
      };

    case SET_TASKS:
      return {
        ...state,
        tasks: action.payload,
      };

    case SET_NOTIFICATIONS:
      return {
        ...state,
        notifications: action.payload,
      };
    case SET_UNREAD_NOTIFICATIONS:
      return {
        ...state,
        unreadNotifications: action.payload,
      };

    case TOGGLE_MESSAGE:
      return {
        ...state,
        isMessageOpen: !state.isMessageOpen,
      };

    case SET_MESSAGE_TYPE:
      return {
        ...state,
        messageType: action.payload,
      };

    default:
      return state;
  }
};
