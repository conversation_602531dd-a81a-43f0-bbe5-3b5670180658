export interface LeadAssignment {
  id?: number;
  user_id: string;
  lead_id: number;
  status: string;
  sub_status: string;
  assignment_type: string;
  assignment_method: string;
  created_at?: Date;
  updated_at?: Date;
  lead_name?: string;
}

export interface LeadAssignmentParams {
  id: number;
  user_id: string;
  status?: string;
  sub_status?: string;
  updated_at?: Date;
}

export interface LeadAssignmentSearchParams {
  user_id?: string;
  assigned_user_id?: string;
  status?: string;
  sub_status?: string;
  createdAt_from?: string;
  createdAt_to?: string;
  grades?: Array<string>;
  sources?: Array<string>;
  search_notes?: string;
  assigned_schools?: boolean;
  year?: string;
  language?: string;
  lead_source_id?: number;
  lead_status_id?: number;
  sync_intg_status?: string;
  school_id?: number;
  textSearch?: string;
  viewed_leads?: 'viewed' | 'not-viewed';
  custom_field_1?: string;
  custom_field_2?: string;
  custom_field_3?: string;
  custom_field_4?: string;
  custom_field_5?: string;
  page: string;
  pageSize: string;
  fieldSort: string;
  fieldDirection: string;
}

export interface ManagementLeadAssignmentSearchParams {
  assignedStatus: 'all'|'unassigned'|'assigned'
  status: 'new'|'application-received'
  schoolId?: number
  page: number
  pageSize: number
}

export interface AnalyticsParams {
  from?: string;
  to?: string;
}

export interface Top3Params {
  grade: string;
  zip: string;
  features: string;
  school_types: string;
}

export interface AssignmentCounts {
  general: number
  assigned: number
}
