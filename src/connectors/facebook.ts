/* eslint-disable @typescript-eslint/no-explicit-any */
import axios from 'axios'
import {FacebookAdsApi, AdAccount, Campaign,  Lead, EventRequest , ServerEvent, UserData, CustomData     } from 'facebook-nodejs-business-sdk'

import {
  FACEBOOK_CLIENT_ID,
  FACEBOOK_CLIENT_SECRET,
  FACEBOOK_ACCESS_TOKEN,
  LOGGER
} from '@constants'
import Cursor from 'facebook-nodejs-business-sdk/src/cursor'
import * as crypto from 'crypto'
import { getLogger } from '@config/logger'
import { FacebookConversionEvent} from '../interfaces'
const logger = (LOGGER == 'cloudwatch') ? console : getLogger({service: 'connectors/facebook'})

const ENDPOINT_ACCESS_TOKEN = 'https://graph.facebook.com/v23.0/oauth/access_token?grant_type=fb_exchange_token'

class facebookConnection {
  private static request_token = true

  /**
   * Reresh and access token for facebook connector
   * @param {string } access_token
   * @returns {Promise<void>} a promise
   */
  static async initAndRefreshAccessToken(access_token: string): Promise<void> {
    if(!this.request_token){
      return
    }
    try {
      this.request_token = false
      logger.info('Facebook: Getting access token: ' + access_token)
      const url = ENDPOINT_ACCESS_TOKEN + `&client_id=${FACEBOOK_CLIENT_ID}&client_secret=${FACEBOOK_CLIENT_SECRET}&fb_exchange_token=${access_token}`
      const axios_response =  await axios.get(url)
      const {data} = axios_response
      if(data && data.access_token){
        FacebookAdsApi.init(data.access_token)
        setTimeout(()=>{
          this.request_token = true
          this.initAndRefreshAccessToken(data.access_token)
        }, 3600000)
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        console.error('facebookConnection.refreshAccessToken > error', error.message)
      } else {
        console.error('facebookConnection.refreshAccessToken > error', error)
      }
    }
  }
}

facebookConnection.initAndRefreshAccessToken(FACEBOOK_ACCESS_TOKEN)

/**
 * Gets campaign's insights
 * @param {string | number} campaign_id
 * @param {string[]} fields
 * @param {Record<string, any>} params
 * @returns {Promise<Cursor>} a promise of cursor object
 */
export async function getCampaignInsights(campaign_id: string | number,fields: string[], params?: Record<string, any>): Promise<Cursor> {
  const campaign = new Campaign(campaign_id)
  try {
    return await campaign.getInsights(fields, params)
  } catch (error) {
    logger.error('getCampaignInsightsError:', error)
  }
}

/**
 * Gets the account's campaigns
 * @param {string} account_id
 * @param {string[]} fields
 * @param {Record<string, any>} params
 * @returns {Promise<Cursor>} a promise of cursor object
 */
export async function getAdAccountCampaigns(account_id: string ,fields: string[], params?: Record<string, any>): Promise<Cursor> {
  const ad_account = new AdAccount('act_' + account_id)
  const result = await ad_account.getCampaigns(fields, params)
  return result
}

export async function getLead(lead_id: string | number, fields: string[], params?: Record<string, any>): Promise<Lead> {
  const lead = new Lead(lead_id)
  const result = await lead.get(fields, params)
  return result
}

export async function createConversionEvent(req: FacebookConversionEvent) : Promise<boolean> {
  try {
    const hashData = (data: string)=> crypto.createHash('sha256').update(data).digest('hex')
    const eventTime=Math.floor(Date.now() / 1000)

    const userData = new UserData()
      .setClientUserAgent('N/A')
    if(req.payload.email) userData.setEmail(hashData(req.payload.email))
    if(req.payload.phone) userData.setPhone(hashData(req.payload.phone))
    if(req.payload.fbclid) userData.setFbc('fb.1.'+eventTime+'.'+req.payload.fbclid)
    userData.setExternalId(req.payload.leadgen_id || req.payload.fbclid)
    // Validate lead ID format (15-16 digits)
    if (req.payload.leadgen_id) {
      const leadId = req.payload.leadgen_id.toString()
      if (!/^\d{15,16}$/.test(leadId)) {
        console.log(`Invalid Facebook Lead ID format: ${leadId}. Must be 15-16 digits`)
        return false
      }
      userData.setLeadId(leadId)
    }

    const customData = new CustomData()
    customData.setCustomProperties({
      lead_event_source : 'Schola',
      event_source: 'crm'
    })


    let uniqueid=req.payload.lead_id
    if(req.payload.fbclid) uniqueid=req.payload.lead_id
    if(req.payload.leadgen_id) uniqueid=req.payload.leadgen_id
    // Crear el evento
    const serverEvent = new ServerEvent()
      .setEventName(req.eventName)
      .setEventTime(eventTime)  // timestamp
      .setUserData(userData)
      .setEventSourceUrl('https://api.schola.com')
      .setActionSource('system_generated')
      .setEventId(uniqueid)  // for deduplicate

    // Configurar el Event Request
    const eventsData = [serverEvent]
    const eventRequest = (new EventRequest(FACEBOOK_ACCESS_TOKEN, '1367208973344498')) //token, fb pixel id
      .setEvents(eventsData)

    // Enviar el evento
    return eventRequest.execute()
      .then((response: any) => {
        console.log('meta event created:', response)
        return true
      })
      .catch((error: any) => {
        console.error('meta event failed:', error)
        return false
      })
  } catch(error) {
    console.log(error)
  }
}
