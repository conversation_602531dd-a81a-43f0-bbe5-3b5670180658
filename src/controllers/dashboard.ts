import moment, { Moment } from 'moment'

// Import utils functions
import {validate, getObjectValues} from '@utils'

// Import error handlers
import {serverError, notFoundError} from '@errors'

// Import object schemas
import * as schemas from '@schemas'

// Import queries
import { schools_queries, dashboard_queries as queries } from '@queries'

// Import postgres connector
import postgres from '@connectors/postgres'

//Import interfaces
import {
  School,
  GetData,
  GroupLeadHistory,
  LeadsHistory,
  OverviewData,
  ProgressData,
  GetOverviewDataResponse,
  ApplicationsProgressData,
  OverviewDataCurrVs,
  PerformanceByStatusDates,
  Performance,
  StatusByMonth,
  PerformanceByStatusPromise,
  CountPerformanceDBresponse,
  SPOTMessageCount,
} from '@interfaces'

// Import constants
import {PLAN_ID_PRO_M, STATUSES, PLAN_ID_PAYG} from '@constants'

/**
 * Dashboard controller functions
 * Used by /src/routes/dashboard
 */

/**
 * @param  {GetData} params
 * @param  {number} params.school_id
 * @param  {string} params.startDate
 * @param  {string} params.endDate
 * @returns {Promise<GetOverviewDataResponse>} Promise object with the formatted response
 */
export async function getOverviewData (params: GetData): Promise<GetOverviewDataResponse> {
  await validate(params, schemas.get_data)
  const {school_id} = params
  try {
    const db_response = await postgres.query(schools_queries.getSchool(), [school_id])
    const school = db_response.rows[0]
    if(!school) throw notFoundError
    let is_schola_recruiter_pro = false
    if(school.plan_id === PLAN_ID_PRO_M && school.subscription_months > 0 && moment(school.subscription_end).isAfter()) {
      is_schola_recruiter_pro = true
    }
    if(school.plan_id === PLAN_ID_PAYG) {
      is_schola_recruiter_pro = true
    }

    const {startDate, endDate} = params

    const start_date = moment(startDate)
    const end_date = moment(endDate).add(1, 'days')
    const days_diff = moment.duration(start_date.diff(end_date)).asDays()
    const vs_start_date = moment(startDate).add(days_diff, 'days')
    const vs_end_date = moment(startDate)

    const formatted_start_date = vs_start_date.format('YYYYMMDD')
    const formatted_end_date = end_date.format('YYYYMMDD')

    const get_lead_counts_values = [
      formatted_start_date,
      formatted_end_date,
      school_id,
      formatted_start_date,
      formatted_end_date
    ]
    const db_response_leads_history = await postgres.query(queries.getLeadHistory(), get_lead_counts_values)
    const leads_history:Array<LeadsHistory> = db_response_leads_history.rows

    const vs_group_leads = getGroupLeadHistory(leads_history, vs_start_date, vs_end_date)
    const current_group_leads = getGroupLeadHistory(leads_history, start_date, end_date)

    const vs_process_results = processGroupLeadHistoryForOverviewData(vs_group_leads)
    const current_process_results = processGroupLeadHistoryForOverviewData(current_group_leads)

    // Calculate projected ROI
    const projected_roi: OverviewDataCurrVs = {
      versus: 0,
      current: 0
    }
    if(is_schola_recruiter_pro) {
      const state_fund = await getStateContribution(school)
      let plan_price = 4000
      if(school.subscription_months >= 6) plan_price = 3500
      plan_price = (plan_price * school.subscription_months)
      if(current_process_results.applications) {
        projected_roi.current = ((((current_process_results.applications * state_fund) - plan_price) / plan_price) * 100).toFixed(1)
      }
      if(vs_process_results.applications) {
        projected_roi.versus = ((((vs_process_results.applications * state_fund) - plan_price) / plan_price) * 100).toFixed(1)
      }
    }

    // Conversion rate
    const conversion_rate: OverviewDataCurrVs = {
      current: 0,
      versus: 0
    }
    if(current_process_results.currentEnrollments && current_process_results.applications) {
      conversion_rate.current = ((current_process_results.currentEnrollments * 100) /  current_process_results.applications).toFixed(1)
    }
    if(vs_process_results.currentEnrollments && vs_process_results.applications) {
      conversion_rate.versus = ((vs_process_results.currentEnrollments * 100) /  vs_process_results.applications).toFixed(1)
    }

    // Applications received
    const applications_received = {
      current: current_process_results.applications,
      versus: vs_process_results.applications,
      details: [
        {
          name: 'Application Received',
          color: '#008CFF',
          value: current_process_results.applicationsReceived
        },
        {
          name: 'Current Enrollments',
          color: '#5DCDD3',
          value: current_process_results.currentEnrollments
        },
        {
          name: 'Archived Applications',
          color:  '#F0AD1B',
          value:  current_process_results.archivedApplications
        },
        {
          name:'Waitlisted Applications',
          color: '#C2185B',
          value: current_process_results.applicationsWaitlisted
        },
      ]
    }

    // Current enrollments
    const current_enrollments = {
      current: current_process_results.currentEnrollments,
      versus: vs_process_results.currentEnrollments,
      details: [
        {
          name:'Current',
          color: '#5DCDD3',
          value: current_process_results.currentEnrollmentsDetails.current
        },
        {
          name:'Next Year',
          color: '#FC6E35',
          value: current_process_results.currentEnrollmentsDetails.nextYear
        }
      ]
    }

    // Archived applications
    const archived_applications = {
      current: current_process_results.archivedApplications,
      versus: vs_process_results.archivedApplications,
      details: {
        totalApplications: current_process_results.applications,
        outApplications: current_process_results.archivedApplications
      }
    }

    // Guarantee: 3 applications by month ?

    return {
      versusDate: {
        start: vs_start_date.format('MM-DD-YYYY'),
        end: vs_end_date.add(-1, 'days').format('MM-DD-YYYY')
      },
      currentDate: {
        start: start_date.format('MM-DD-YYYY'),
        end: end_date.add(-1, 'days').format('MM-DD-YYYY')
      },
      guarantee: (is_schola_recruiter_pro ? school.subscription_months * 3: 0),
      goal: school.enrollment_goal,
      conversionRate: conversion_rate,
      projectedROI: projected_roi,
      applicationsReceived: applications_received,
      currentEnrollments: current_enrollments,
      archivedApplications: archived_applications,
    }
  } catch (error) {
    console.log('getOverviewDataError: ', error, params)
    if(error.statusCode === notFoundError.statusCode) throw error
    throw serverError
  }
}


export async function getLeadsByMonth({schoolId, sinceDate}: {schoolId: string, sinceDate: string}): Promise<StatusByMonth[]> {
  if (!schoolId) return null

  try {
    const diffInDays = moment().diff(moment(sinceDate, 'YYYY/MM/DD'), 'days')

    let unitOfTime: 'days' | 'month'
    let rangeCount
    if (diffInDays >= 60) {
      unitOfTime = 'month'
      rangeCount = Math.ceil(diffInDays / 30) // Approximate months
    } else if (diffInDays >= 0) {
      unitOfTime = 'days'
      rangeCount = Math.ceil(diffInDays / 7) // Approximate weeks
    }

    const data = await postgres.query(queries.getLeadsStatusByMonth(), [unitOfTime, schoolId, sinceDate])
    const historyLeads = data.rows

    if(unitOfTime === 'month') {
      const rangeMonths = []
      const formatRange = 'MMM-YYYY'
      for (let i = 0; i < rangeCount; i++) {
        rangeMonths.push(moment(sinceDate, 'YYYY/MM/DD').add(i, unitOfTime).format(formatRange))
      }

      const mergedTimeline = rangeMonths.map((month) => {
        const match = historyLeads.find(
          (lead) => moment.utc(lead.month).format(formatRange) === month
        )

        return {
          month,
          Leads: +match?.new || 0,
          Applications: +match?.applications || 0,
          Enrollments: +match?.enrollments || 0,
        }
      })

      return mergedTimeline

    } else if(unitOfTime === 'days') {
      const rangeWeeks = []
      const formatRange = 'MMM-D'

      for (let i = 0; i <= diffInDays; i++) {
        rangeWeeks.push(moment(sinceDate, 'YYYY/MM/DD').add(i, 'days').format(formatRange))
      }

      const mergedTimeline = rangeWeeks.map((week) => {
        const match = historyLeads.find(
          (lead) => moment.utc(lead.month).format(formatRange) === week
        )

        return {
          month: week,
          Leads: +match?.new || 0,
          Applications: +match?.applications || 0,
          Enrollments: +match?.enrollments || 0,
        }
      })

      return mergedTimeline
    }

  } catch (error) {
    console.log(error.message)
  }
}

export async function getPerformanceByStatus({ schoolId, today, rangeDate, previousRangeDate }: PerformanceByStatusDates): Promise<PerformanceByStatusPromise> {
  try {
    //const query = await postgres.query(queries.getNewLeadsRange(), [schoolId, today, rangeDate, previousRangeDate])
    const query = await postgres.query(queries.getNewLeadsRange(), [schoolId, rangeDate])
    //const { previous_new, current_new, previous_applications, current_applications, previous_enrollments, current_enrollments } = query.rows[0] as CountPerformanceDBresponse
    const { current_new, current_applications, current_enrollments } = query.rows[0] as CountPerformanceDBresponse

    // const newLeadsPerformance = getRelativePerformance({ previous: previous_new, current: current_new })
    // const applicationsPerformance = getRelativePerformance({ previous: previous_applications, current: current_applications })
    // const enrollmentPerformance = getRelativePerformance({ previous: previous_enrollments, current: current_enrollments })

    return {
      new: current_new,
      //newLeadsPerformance,
      applications: current_applications,
      //applicationsPerformance,
      enrollments: current_enrollments,
      //enrollmentPerformance
    }

  } catch (error) {
    console.log(error.message)
  }
}

// const getRelativePerformance = ({ previous, current }: { previous: string, current: string }): Performance => {
//   const previousValue = Number(previous)
//   const currentValue = Number(current)

//   if(previousValue === 0) return 'firstPerformance'

//   const performance = (currentValue - previousValue) / previousValue * 100
//   return performance
// }


export const getMessagesBySPOT = async ({ schoolId, sinceDate }: { schoolId: number, sinceDate: string }): Promise<SPOTMessageCount> => {
  try {
    const query = await postgres.query(queries.getMessagesBySPOT(), [schoolId, sinceDate])
    const data = query.rows

    // filter by type message:
    const emailsCount = calculateMessageCount(data, 'emails')
    const smsCount = calculateMessageCount(data, 'sms')

    return { emailsCount, smsCount }

  } catch (error) {
    console.log(error.message)
  }
}
const calculateMessageCount = (messages: { source:'emails' | 'sms', total_count: string }[], type: 'emails' | 'sms'): number => {
  const calculateMessages = messages.filter((item) => item.source === type).reduce((accumulate, current) => accumulate + Number(current.total_count), 0)
  return calculateMessages
}

/**
 * @param  {GetData} params
 * @param  {number} params.school_id
 * @param  {string} params.startDate
 * @param  {string} params.endDate
 * @returns {Promise<ApplicationsProgressData>} Promise object with the formatted response
 */
export async function getApplicationsProgressData(params: GetData): Promise<ApplicationsProgressData> {
  await validate(params, schemas.get_data)
  const {school_id} = params
  try {
    const db_response = await postgres.query(schools_queries.getSchool(), [school_id])
    const school = db_response.rows[0]
    if(!school) throw notFoundError
    let is_schola_recruiter_pro = false
    if(school.plan_id === PLAN_ID_PRO_M && school.subscription_months > 0 && moment(school.subscription_end).isAfter()) {
      is_schola_recruiter_pro = true
    }
    if(school.plan_id === PLAN_ID_PAYG) {
      is_schola_recruiter_pro = true
    }

    const {startDate, endDate} = params

    const start_date = moment(startDate)
    const end_date = moment(endDate).add(1, 'days')

    const formatted_start_date = start_date.format('YYYYMMDD')
    const formatted_end_date = end_date.format('YYYYMMDD')

    const get_lead_counts_values = [
      formatted_start_date,
      formatted_end_date,
      school_id,
      formatted_start_date,
      formatted_end_date
    ]
    const db_response_leads_history = await postgres.query(queries.getLeadHistory(), get_lead_counts_values)
    const leads_history:Array<LeadsHistory> = db_response_leads_history.rows
    const group_leads = getGroupLeadHistory(leads_history, start_date, end_date)
    const process_result = processGroupLeadHistoryForApplicationsProgressData(group_leads, start_date, end_date)

    let applications = 0
    let current_enrollment = 0
    let next_year = 0
    let waitlisted = 0
    let archived = 0
    return {
      applicationGuarantee: (is_schola_recruiter_pro ? school.subscription_months * 3 : 0),
      data: process_result.map( d=> {
        const date = d.isMonthly ? d.date.format('MMMM YYYY') : d.date.format('MM-DD-YYYY')
        applications += d.applicationsreceived
        current_enrollment += d.currentEnrollment
        next_year += d.nextYear
        waitlisted += d.waitlisted
        archived += d.archived
        return {
          date,
          applications,
          currentEnrollment: current_enrollment,
          nextYear: next_year,
          waitlisted,
          archived
        }
      })
    }
  } catch (error) {
    console.error('getApplicationsProgressDataError:', error)
    if(error.statusCode === notFoundError.statusCode) throw error
    throw serverError
  }
}

/**
 * @param  {GetData} params
 * @param  {number} params.school_id
 * @param  {string} params.startDate
 * @param  {string} params.endDate
 * @returns {Promise<Array<unknown>>} Promise object with the formatted response
 */
export async function getCurrentLeadPipelineData(params: GetData): Promise<Array<unknown>> {
  await validate(params, schemas.get_data)
  try {
    const values = await getObjectValues(params)
    const db_response = await postgres.query(queries.getLeadCounts(), values)
    const lead_counts = db_response.rows

    let incoming_amount = 0
    let application_sent_amount = 0
    let application_received_amount = 0
    let waitlisted_amount = 0
    let accepted_amount = 0
    for (let index = 0; index < lead_counts.length; index++) {
      const element = lead_counts[index]
      const {status} = element
      const count = parseInt(element.count)
      if(status === STATUSES.new) {
        incoming_amount = count
        continue
      }
      if(status === STATUSES.application_sent) {
        application_sent_amount = count
        continue
      }
      if(status === STATUSES.application_received) {
        application_received_amount = count
        continue
      }
      if(status === STATUSES.waitlisted) {
        waitlisted_amount = count
        continue
      }
      if(status === STATUSES.accepted) {
        accepted_amount = count
        continue
      }
    }
    return [
      {
        stage: 'Incoming',
        amount: incoming_amount,
        ats: '-'
      },
      {
        stage: 'Application sent',
        amount: application_sent_amount,
        ats: '-'
      },
      {
        stage: 'Application received',
        amount: application_received_amount,
        ats: '-'
      },
      {
        stage: 'Waitlisted',
        amount: waitlisted_amount,
        ats: '-'
      },
      {
        stage: 'Accepted',
        amount: accepted_amount,
        ats: '-'
      },
    ]

  } catch (error) {
    console.error('getCurrentLeadPipelineDataError:', error)
    if(error.statusCode === notFoundError.statusCode) throw error
    throw serverError
  }

}

/**
 * @param  {Array<LeadsHistory>} leads
 * @param  {Moment} start_date
 * @param  {Moment} end_date
 * @returns {Array<GroupLeadHistory>} Array of group lead history
 */
export function getGroupLeadHistory(leads: Array<LeadsHistory>, start_date: Moment, end_date: Moment): Array<GroupLeadHistory> {
  const group: Array<GroupLeadHistory> = []
  leads.forEach( lead_record => {
    const status_updated_at = moment(lead_record.status_updated_at)
    if(status_updated_at >= start_date && status_updated_at < end_date) {
      const key = lead_record.lead_id
      const found_group = group.find(x => x.key === key)
      if(found_group) {
        found_group.items.push(lead_record)
      } else {
        const new_group = {
          key: key,
          items: [] as Array<LeadsHistory>
        }
        new_group.items.push(lead_record)
        group.push(new_group)
      }
    }
  })
  return group
}

/**
 * @param  {Array<GroupLeadHistory>} groups
 * @returns {OverviewData} The overview data counted by status
 */
export function processGroupLeadHistoryForOverviewData (groups: Array<GroupLeadHistory>): OverviewData {
  const result = {
    applications: 0,
    applicationsReceived: 0,
    applicationsWaitlisted: 0,
    currentEnrollments: 0,
    archivedApplications: 0,
    currentEnrollmentsDetails:{
      current: 0,
      nextYear: 0,
    }
  }
  groups.forEach( group => {
    const lead = group.items[0]
    let application = lead.application_received || lead.has_completed_application
    if (application && !lead.application_valid) {
      application = false
    }
    if(application) {
      result.applications += 1
      const is_current_year = isNotNextYear(lead.lead_created_at, lead.lead_year)
      let application_received = false
      let waitlisted = false
      let archived = false
      let accepted = false
      for (let index = 0; index < group.items.length && !(application_received && waitlisted && archived && accepted); index++) {
        const lead_record = group.items[index]
        const {current_status, new_status} = lead_record
        switch (current_status) {
        case STATUSES.application_received:
          application_received = true
          break
        case STATUSES.waitlisted:
          waitlisted = true
          break
        case STATUSES.archived:
          archived = true
          if(new_status === STATUSES.accepted) accepted = true
          break
        case STATUSES.accepted:
          accepted = true
          break
        }
      }
      if(application_received) result.applicationsReceived += 1
      if(waitlisted) result.applicationsWaitlisted += 1
      if(archived) result.archivedApplications += 1
      if(accepted) {
        result.currentEnrollments += 1
        if( is_current_year) {
          result.currentEnrollmentsDetails.current += 1
        } else {
          result.currentEnrollmentsDetails.nextYear += 1
        }
      }
    }
  })
  return result
}

/**
 * @param  {School} school - school object
 * @returns {Promise<number>} - This number is either the avg_per_pupil number or the amount from the state fundings table
 */
export async function getStateContribution(school: School): Promise<number> {
  const {avg_per_pupil} = school
  if(avg_per_pupil) {
    return avg_per_pupil
  } else {
    const db_response = await postgres.query(schools_queries.getAmountFromStateFundings(), [school.id])
    if(db_response.rows[0]) {
      const amount = parseFloat(db_response.rows[0].amount.replace('$','').replace(',', ''))
      return amount
    }
    return 0
  }
}

/**
 * @param  {Array<GroupLeadHistory>} groups
 * @param  {Moment} start_date
 * @param  {Moment} end_date
 * @returns {Array<ProgressData>}
 */
export function processGroupLeadHistoryForApplicationsProgressData(groups: Array<GroupLeadHistory>, start_date: Moment, end_date: Moment): Array<ProgressData> {
  const days_diff = moment.duration(end_date.diff(start_date)).asDays()
  const months_diff = moment.duration(end_date.diff(start_date)).asMonths()
  const result:Array<ProgressData> = []
  let format_key:string
  if(days_diff < 30) {
    format_key = 'YYYY-MM-DD'
    const start = moment(start_date.format('YYYYMMDD'))
    for (let index = 0; index < days_diff; index++) {
      result.push({
        key: start.format(format_key),
        date: moment(start),
        isMonthly: false,
        applications: 0,
        applicationsreceived: 0,
        currentEnrollment: 0,
        nextYear: 0,
        waitlisted: 0,
        archived: 0
      })
      start.add(1, 'days')
    }
  } else {
    format_key = 'YYYY-MM'
    const start = moment(`${start_date.format('YYYYMM')}01`)
    for (let index = 0; index <= months_diff; index++) {
      result.push({
        key: start.format(format_key),
        date: moment(start),
        isMonthly: true,
        applications: 0,
        applicationsreceived: 0,
        currentEnrollment: 0,
        nextYear: 0,
        waitlisted: 0,
        archived: 0
      })
      start.add(1, 'months')
    }
  }
  groups.forEach( group => {
    const lead = group.items[0]
    let application = lead.application_received || lead.has_completed_application
    if(application && !lead.application_valid) {
      application = false
    }

    if(application) {
      const is_current_year = isNotNextYear(lead.lead_created_at, lead.lead_year)
      let application_received = false
      let waitlisted = false
      let archived = false
      let accepted = false
      for (let index = 0; index < group.items.length; index++) {
        const lead_record = group.items[index]
        const {current_status} = lead_record
        const key = moment(lead_record.status_updated_at).format(format_key)
        const found_group = result.find( x => x.key === key)
        if(found_group) {
          found_group.applications += 1
          switch (current_status) {
          case STATUSES.application_received:
            if(!application_received) {
              application_received = true
              found_group.applicationsreceived += 1
            }
            break
          case STATUSES.waitlisted:
            if(!waitlisted) {
              waitlisted = true
              if(!accepted) {
                found_group.waitlisted += 1
              }
            }
            break
          case STATUSES.archived:
            if(!archived) {
              archived = true
              found_group.archived += 1
            }
            break
          case STATUSES.accepted:
            if(!accepted) {
              accepted = true
              if(is_current_year) {
                found_group.currentEnrollment += 1
              } else {
                found_group.nextYear += 1
              }
            }
            break
          }
        }
      }
    }
  })
  return result
}

/**
 * @param  {Moment} lead_created_at - Lead created_at date
 * @param  {string} lead_year - Lead year in the format of '2022-2023'
 * @returns {boolean} Whether the lead created_at is the current year or not
 */
export function isSameYear(lead_created_at: Moment, lead_year:string): boolean {
  const year = moment(lead_created_at).year()
  const current_lead_year = `${year}-${year+1}`
  return current_lead_year === lead_year
}

// We expect this function to be replaced by isSameYear
export function isNotNextYear(lead_created_at: Moment, lead_year:string): boolean {
  const year = moment(lead_created_at).year()
  const next_year = `${year + 1}-${year + 2}`
  return next_year !== lead_year
}
