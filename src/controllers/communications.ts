//Import utils functions
import { validate, getObjectValues } from '@utils'

//Import moment to get time
import moment from 'moment'

//Import object schemas
import * as schemas from '@schemas'

//Import queries
import { leads_queries as l_queries,
  user_profile_queries as up_queries,
  communications_queries as com_queries,
  reasons_queries as r_queries,
  insertQuery
} from '@queries'

//Import connectors
import postgres from '@connectors/postgres'
import { getCallInfo } from '@connectors/dialpad-services'

//Import interfaces
import {
  CallStats,
  DataCall,
  DataCallCloudTalk,
  EditLeadForSchool,
} from '@interfaces'

// Import constants
import { DISPOSITION_ARCHIVED_REASON, DISPOSITION_SUBSTAGE } from '@constants'

// Import controllers
import { leadsController, notesController } from '@controllers'

/** Create a call record
 * @param  {DataCall} params
 * @returns {Promise} response object with the statusCode and the response from the database
 */
export async function saveCall(params: DataCall) {
  await validate(params, schemas.save_call)
  try{
    const split_disp = params.call_dispositions.split(/[,\s]+/)
    let school_id = split_disp.pop()
    let pop_1 = split_disp.pop()
    while ((!(/^\d+$/).test(school_id) || pop_1 != '-') && (school_id != undefined && pop_1 != undefined)){
      school_id = pop_1
      pop_1 = split_disp.pop()
    }

    if (split_disp.length == 0 || !(/^\d+$/).test(school_id)){
      return { message: 'saveCall - School id not found in call dispositions', type: 'notFound' }
    }

    const call_info = await getCallInfo(params.call_id)

    const call_dis = params.call_dispositions.toLowerCase()
    const key_words = ['voicemail', 'mailbox', 'service', 'wrong', 'dnc', 'busy']
    const send_survey = key_words.some(word => call_dis.includes(word))
    if (!send_survey){
      try {
        await leadsController.feedbackSurvey({'phone': call_info.external_number})
        console.log(`sent survey call ID - ${params.call_id}`)
      } catch (error) {
        console.error(`saveCall - sent survey error call ID - ${params.call_id}`)
      }
    }

    const tmp_phone = call_info.external_number.substring(call_info.external_number.length - 10, call_info.external_number.length)
    const l_db_res = await postgres.query(l_queries.getLeadBySchoolIdAndPhone(), [tmp_phone, school_id])
    const lead = l_db_res?.rows[0]

    if (lead == undefined){
      console.error(`saveCall - Lead not found - ${params.call_id} ${tmp_phone}`)
      return { message: 'saveCall - Lead not found', type: 'notFound' }
    }

    const user_name = params.caller_email.split('@')[0]
    const up_db_res = await postgres.query(up_queries.getUserProfileByUserId(), [user_name])
    const user = up_db_res?.rows[0]

    if (user == undefined){
      console.error(`saveCall -  User not found - ${params?.caller_email}`)
      return { message: 'saveCall - User not found', type: 'notFound' }
    }

    const now = moment.utc()

    const call_data = {
      school_id: Number(school_id),
      lead_id: lead.id,
      user_id: user.user_id,
      call_id: params.call_id,
      dispositions: params.call_dispositions,
      duration: call_info.duration.toFixed(2),
      total_duration: call_info.total_duration.toFixed(2),
      recording_url: (Object.prototype.hasOwnProperty.call(call_info, 'recording_url')) ? call_info.recording_url[0] : '',
      description: '',
      created_at: now
    }

    const dispositions_arr = params.call_dispositions.split(', ')
    const dispositions_str = dispositions_arr.filter(ele => !ele.includes(school_id)).join(', ')

    try{
      const values: any = {}
      for (const disposition of dispositions_arr){
        if (Object.keys(DISPOSITION_ARCHIVED_REASON).includes(disposition.toLowerCase())){
          const disp = DISPOSITION_ARCHIVED_REASON[disposition.toLowerCase()]
          const substage = DISPOSITION_SUBSTAGE[disposition.toLowerCase()]
          values['status'] = 'archived'
          const substage_db_res = await postgres.query(l_queries.getInsertLeadSubstage(), [school_id, substage])
          values['lead_status_id'] = substage_db_res.rows[0].id
          const reason_db_res = await postgres.query(r_queries.getReasonByName(), [disp])
          values['reason_id'] = reason_db_res.rows[0].id
          const lead_to_update = {
            school_id: Number(school_id),
            leads_ids: [lead.id],
            values,
            user_id: 'system-auto-archive-savecall'
          } as EditLeadForSchool
          await leadsController.editLeadsForSchool(lead_to_update)
          break
        }
      }
    }catch (error){
      console.log(`auto-archive-error-savecall - ${error}`)
    }

    try{
      const values = await getObjectValues(call_data)
      const call_db_res = await postgres.query(insertQuery('calls', call_data), values)
      await notesController.createNote({
        object_type: 'lead',
        object_id: lead.id,
        user_id: params.caller_email,
        note: dispositions_str
      })
      return { call_info: call_db_res.rows[0] }
    } catch (error) {
      throw { message: 'saveCall - Duplicated Call ID', type: 'Duplicated' }
    }

  } catch (error) {
    console.error('saveCall:', error)
    throw error
  }
}

/** Create a call record
 * @param  {DataCallCloudTalk} params
 * @returns {Promise} response object with the statusCode and the response from the database
 */
export async function saveCallCloudTalk(params: DataCallCloudTalk) {
  await validate(params, schemas.save_call_cloudtalk)
  const now = moment.utc()

  const time_call_id = now.format('YYYYMMDDHHmmss')+'-'+params.call_id

  console.log(`${time_call_id} - saveCallCloudTalk - params:`, JSON.stringify(params, null, 2))

  if (params?.caller_email == undefined || (typeof params?.caller_email != 'string')){
    console.log(`${time_call_id} - saveCallCloudTalk - caller_email is undefined or not a string`)
    console.log(params?.caller_email)
    return {}
  }

  if (params?.tags == undefined || (typeof params?.tags != 'string')){
    console.log(`${time_call_id} - saveCallCloudTalk - tags is undefined or not a string`)
    console.log(params?.tags)
    return {}
  }

  try{
    const split_disp = params.tags.split(/[,\s]+/)
    let school_id = split_disp.pop()
    let pop_1 = split_disp.pop()
    while ((!(/^\d+$/).test(school_id) || pop_1 != '-') && (school_id != undefined && pop_1 != undefined)){
      school_id = pop_1
      pop_1 = split_disp.pop()
    }

    if (split_disp.length == 0 || !school_id || !(/^\d+$/).test(school_id)){
      console.log(`${time_call_id} - saveCallCloudTalk - School id not found in call dispositions`)
      return { message: `saveCallCloudTalk - ${params.call_id} - School id not found in call dispositions`, type: 'notFound' }
    }

    const call_dis = params.tags.toLowerCase()
    const key_words = ['voicemail', 'mailbox', 'service', 'wrong', 'dnc', 'busy']
    const send_survey = key_words.some(word => call_dis.includes(word))
    if (!send_survey){
      try {
        await leadsController.feedbackSurvey({'phone': `+${params.external_number}`})
        console.log(`${time_call_id} - sent survey call ID`)
      } catch (error) {
        console.error(`${time_call_id} - saveCallCloudTalk - sent survey error call ID`)
      }
    }

    const tmp_phone = params.external_number.substring(params.external_number.length - 10, params.external_number.length)
    const l_db_res = await postgres.query(l_queries.getLeadBySchoolIdAndPhone(), [tmp_phone, school_id])
    const lead = l_db_res?.rows[0]

    if (lead == undefined){
      console.error(`${time_call_id} - saveCallCloudTalk - Lead not found - ${tmp_phone}`)
      return { message: `saveCallCloudTalk - ${time_call_id} - Lead not found`, type: 'notFound' }
    }

    const user_name = params.caller_email.split('@')[0]
    const up_db_res = await postgres.query(up_queries.getUserProfileByUserId(), [user_name])
    const user = up_db_res?.rows[0]

    if (user == undefined){
      console.error(`${time_call_id} - saveCallCloudTalk -  User not found - ${params?.caller_email}`)
      return { message: `saveCallCloudTalk - ${time_call_id} - User not found`, type: 'notFound' }
    }

    const call_data = {
      school_id: Number(school_id),
      lead_id: lead.id,
      user_id: user.user_id,
      call_id: params.call_id,
      dispositions: params.tags,
      duration: params.talking_time.toString(),
      total_duration: (params.talking_time + params.waiting_time),
      recording_url: (Object.prototype.hasOwnProperty.call(params, 'recording_url')) ? params.recording_url : '',
      description: '',
      created_at: now
    }

    const dispositions_arr = params.tags.split(/,\s*/)
    if(params.note != null)
      dispositions_arr.push(params.note.replace(/"[^"]*"\s*/, '').replace('\n', ', '))

    let dispositions_str = ''
    if (call_dis.includes('voicemail dropped')){
      dispositions_str = 'Parent did not answer, left a voicemail'
    } else {
      dispositions_str = dispositions_arr.filter(ele => !ele.includes(school_id)).join(', ')
    }

    try{
      const values: any = {}
      for (const disposition of dispositions_arr){
        if (Object.keys(DISPOSITION_ARCHIVED_REASON).includes(disposition.toLowerCase())){
          const disp = DISPOSITION_ARCHIVED_REASON[disposition.toLowerCase()]
          const substage = DISPOSITION_SUBSTAGE[disposition.toLowerCase()]
          values['status'] = 'archived'
          const substage_db_res = await postgres.query(l_queries.getInsertLeadSubstage(), [school_id, substage])
          values['lead_status_id'] = substage_db_res.rows[0].id
          const reason_db_res = await postgres.query(r_queries.getReasonByName(), [disp])
          values['reason_id'] = reason_db_res.rows[0].id
          const lead_to_update = {
            school_id: Number(school_id),
            leads_ids: [lead.id],
            values,
            user_id: 'system-auto-archive-savecall'
          } as EditLeadForSchool
          await leadsController.editLeadsForSchool(lead_to_update)
          break
        }
      }
    }catch (error){
      console.log(`${time_call_id} - auto-archive-error-savecall - ${error}`)
    }

    try{
      const values = await getObjectValues(call_data)
      const call_db_res = await postgres.query(insertQuery('calls', call_data), values)
      await notesController.createNote({
        object_type: 'lead',
        object_id: lead.id,
        user_id: params.caller_email,
        note: dispositions_str
      })
      return { call_info: call_db_res.rows[0] }
    } catch (error) {
      throw { message: `saveCallCloudTalk - ${time_call_id} - Duplicated Call ID`, type: 'Duplicated' }
    }

  } catch (error) {
    console.error(`${time_call_id} - saveCallCloudTalk - ${error}`)
    throw error
  }
}


/**
 * Gets a json of calls that match the filters
 * @param  {DataCall} params
 * @returns {Promise} response object with the statusCode and the response from the database
 */
export async function getCallStats(params: CallStats) {
  try{
    let query = com_queries.getAllStatsBySchoolId()
    if(params.from && params.to){
      query = com_queries.getAllStatsBySchoolIdWithDates()
      params.from = moment(params.from, 'YYYY/MM/DD').utc() as any
      params.to = moment(params.to, 'YYYY/MM/DD').utc() as any
    }
    const values = await getObjectValues(params)
    const call_db_res = await postgres.query(query, values)
    return { call_info: call_db_res.rows[0].json_build_object }
  } catch (error) {
    console.error('getCallStats:', error)
    throw error
  }
}
