import React, { useState, useEffect, useRef } from 'react';
import {
  Typography,
  Drawer,
  Input,
  Select,
  Flex,
  Button,
  Card,
  Space,
  Skeleton,
  Checkbox,
  Col,
  Row,
  Switch,
  Alert,
  DatePicker,
  Tag,
  Tabs,
  Dropdown,
  Modal,
} from 'antd';
import { useSelector } from 'react-redux';
import grades from 'util/grades';
import CreatableSelect from 'react-select/creatable';
import { _isSuperadmin } from 'util/auth';
import { useUpdateLead } from 'components/pages/crm-spot/hooks/useUpdateLead';
import {
  MessageOutlined,
  EditOutlined,
  MailOutlined,
  PhoneOutlined,
  MoreOutlined,
  DeleteOutlined,
  ExclamationCircleFilled,
} from '@ant-design/icons';
import { formatDate } from 'components/pages/crm-spot/utils/format-date';
import { useLeadSources } from 'components/pages/crm-spot/hooks/useLeadSources';
import { useLeadSubStages } from 'components/pages/crm-spot/hooks/useLeadSubStages';
import { Card as ScholaCard } from 'components/card/card';
import moment from 'moment';
import dayjs from 'dayjs';
import GooglePlaces from 'components/google-places-field/google-places-field';
import { SelectionCard } from '.';
import { useNotification } from 'components/pages/crm-spot/layouts/crm-spot-layout';

const { Title, Text } = Typography;

const languageOptions = [
  { value: 'english', label: 'English', uuid: 'english' },
  { value: 'spanish', label: 'Spanish', uuid: 'spanish' },
];

export const LeadEdition = ({
  userId,
  notesHook,
  open,
  onClose,
  getLeadAssignments,
  isCallActive,
  updateAssignment,
  schoolFields,
  loading: loadingStatus,
}) => {
  const { selectedLead } = useSelector((state) => state.spot);

  // Local state for the form
  const [formState, setFormState] = useState(() => ({
    parent_first_name: '',
    parent_last_name: '',
    child_first_name: '',
    child_last_name: '',
    email: '',
    phone: '',
    language: '',
    grade: '',
    address: '',
    city: '',
    state: '',
    zipcode: '',
    lead_status_id: '',
    application_received: false,
    application_valid: false,
    application_received_at: '',
    enrollment_confirmed: true,
    enrollment_confirmed_at: '',
    custom_field_1: '',
    custom_field_2: '',
    custom_field_3: '',
    custom_field_4: '',
    custom_field_5: '',
  }));

  // State for the note input
  const [noteText, setNoteText] = useState('');

  const { fullNotes, createNode, removeNote, updateNote, loading, initialized } = notesHook;

  // Update formState when selectedLead changes
  useEffect(() => {
    setFormState({
      parent_first_name: selectedLead?.parent_first_name || '',
      parent_last_name: selectedLead?.parent_last_name || '',
      child_first_name: selectedLead?.child_first_name || '',
      child_last_name: selectedLead?.child_last_name || '',
      email: selectedLead?.email || '',
      phone: selectedLead?.phone || '',
      language: selectedLead?.language || 'english',
      lead_source_id: selectedLead?.lead_source_id || null,
      lead_status_id: selectedLead?.lead_status_id || null,
      grade: selectedLead?.grade || '',
      address_description: selectedLead?.address_description || '',
      city: selectedLead?.city || '',
      state: selectedLead?.state || '',
      zipcode: selectedLead?.zipcode || '',
      application_valid: selectedLead?.application_valid || undefined,
      application_received: selectedLead?.application_received || undefined,
      application_received_at: selectedLead?.application_received_at || null,
      enrollment_confirmed: selectedLead?.enrollment_confirmed || undefined,
      enrollment_confirmed_at: selectedLead?.enrollment_confirmed_at || null,
      custom_field_1: selectedLead?.custom_field_1 || '',
      custom_field_2: selectedLead?.custom_field_2 || '',
      custom_field_3: selectedLead?.custom_field_3 || '',
      custom_field_4: selectedLead?.custom_field_4 || '',
      custom_field_5: selectedLead?.custom_field_5 || '',
    });
  }, [selectedLead]);

  // Set readonly for Lead Source if needed
  useEffect(() => {
    const readonly =
      formState.lead_source_id < 0 &&
      selectedLead?.created_on !== 'admin' &&
      selectedLead?.created_on !== 'admin-reset' &&
      !_isSuperadmin();

    setLeadSourceReadonly(!!readonly);
  }, [formState.lead_source_id, selectedLead?.created_on]);

  const handleInputChange = (id, value) => {
    setFormState((prev) => ({ ...prev, [id]: value }));
  };

  const [isEditing, setIsEditing] = useState(false);

  const { openNotification } = useNotification();
  const { updateLead } = useUpdateLead();
  const handleUpdate = async () => {
    const schoolId = selectedLead.school_id;
    const leadIds = [selectedLead.id];

    try {
      const res = await updateLead({ schoolId, leadIds, values: formState });
      if (res.status === 409) {
        openNotification({ type: 'error', message: 'Could Not Update', description: 'Detected lead duplicated' });
      }
      if (res.ok) {
        await getLeadAssignments();
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Error updating lead:', error);
    }
  };

  const handleEdit = () => {
    if (isEditing) {
      handleUpdate();
    } else {
      setIsEditing(true);
    }
  };

  const handleAddNote = async () => {
    if (!noteText.trim()) return;
    await createNode({ leadId: selectedLead.id, note: noteText });
    setNoteText('');
  };

  const [leadSourceReadonly, setLeadSourceReadonly] = useState(false);

  const { sources, fetchSources, addLeadSource } = useLeadSources(selectedLead?.school_id);

  // Lead Source select (uses sources directly, no local state)
  const renderSelectLeadSource = () => {
    // Use sources directly, sorted and mapped
    const options = (sources || [])
      .sort((a, b) => (a.name > b.name ? 1 : -1))
      .map((ls) => ({ value: ls.id, label: ls.name, uuid: ls.id }));

    let auxPlaceholderLead = '';
    const selectedOption = options.find((status) => status.value === formState.lead_source_id);
    if (selectedOption) {
      auxPlaceholderLead = selectedOption.label;
    }

    const onChange = (selected) => {
      if (!selected) return;
      setFormState((prev) => ({
        ...prev,
        lead_source_id: selected.value,
      }));
    };

    const onCreateOpt = async (value) => {
      const leadSource = await addLeadSource(value);
      if (leadSource) {
        await fetchSources();
        setFormState((prev) => ({ ...prev, lead_source_id: leadSource.id }));
      }
    };

    return (
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <label htmlFor="source">Lead Source</label>
        {!leadSourceReadonly && (
          <CreatableSelect
            isClearable
            closeOnSelect={false}
            className="lead-select"
            classNamePrefix="schola"
            onChange={onChange}
            onCreateOption={onCreateOpt}
            placeholder={auxPlaceholderLead || 'Select or create a source'}
            options={options}
            isDisabled={!isEditing}
            value={options.find((opt) => opt.value === formState.lead_source_id) || null}
            uuid="lead_source_id"
          />
        )}
        {leadSourceReadonly && <label className="ml-2">{auxPlaceholderLead}</label>}
      </div>
    );
  };
  // Sub stages (statuses)
  const { statuses, fetchStatuses, addLeadStatus } = useLeadSubStages(selectedLead?.school_id);
  const renderSelectLeadStages = () => {
    let options = [];
    statuses &&
      statuses
        .sort((a, b) => (a.name > b.name ? 1 : -1))
        .forEach((ls) => {
          options.push({ value: ls.id, label: ls.name, uuid: ls.id });
        });

    let auxPlaceholderLead;
    try {
      auxPlaceholderLead = options.filter((status) => {
        return status.value === formState.lead_status_id;
      })[0]?.label;
    } catch (error) {}

    const onChange = (selected) => {
      if (!selected) {
        handleInputChange('lead_status_id', '');
        return;
      }
      handleInputChange('lead_status_id', selected.value);
    };

    // Create status, refresh and select
    const onCreateOpt = async (name) => {
      const leadStatus = await addLeadStatus(name);
      if (leadStatus) {
        await fetchStatuses();
        setFormState((prev) => ({ ...prev, lead_status_id: leadStatus.id }));
      }
    };

    return (
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <label htmlFor="lead_sub_stages">Sub Stage</label>
        <CreatableSelect
          isClearable
          closeOnSelect={false}
          className="lead-select"
          classNamePrefix="schola"
          onChange={onChange}
          onCreateOption={onCreateOpt}
          value={options.find((opt) => opt.value === formState.lead_status_id) || null}
          options={options}
          placeholder={auxPlaceholderLead}
          isDisabled={!isEditing}
          uuid="lead_status_id"
        />
      </div>
    );
  };
  const setLocationFields = (locationInfo) => {
    console.log(locationInfo);
    setFormState((prev) => ({
      ...prev,
      address: locationInfo?.data?.description || locationInfo?.address_description || '',
      address_description: locationInfo?.data?.description || locationInfo?.address_description || '',
      state: locationInfo?.state || '',
      city: locationInfo?.city || '',
      zipcode: locationInfo?.zip || '',
      longitude: locationInfo?.longitude || '',
      latitude: locationInfo?.latitude || '',
    }));
  };
  const clearLocationFields = () => {
    setFormState((prev) => ({
      ...prev,
      address: '',
      state: '',
      city: '',
      zipcode: '',
      address_description: '',
      longitude: '',
      latitude: '',
    }));
  };

  const [status, setStatus] = useState('attempting-contact');
  const [isSuccess, setSuccess] = useState(false);
  const handleStatusUpdate = async () => {
    try {
      const updatedAssign = await updateAssignment({
        user_id: userId,
        newValue: status,
      });

      if (updatedAssign) {
        setSuccess(true);
      }
    } catch (error) {
      console.log('handleUpdate', error);
    }
  };

  const [tab, setTab] = useState('info');

  return (
    <Drawer
      open={open}
      width={550}
      placement="right"
      onClose={() => {
        onClose();
        setSuccess(false);
        setStatus('attempting-contact');
      }}
      title={
        <Flex align="center" justify="space-between">
          <Title level={5} style={{ marginBottom: 0 }}>
            {selectedLead?.parent_first_name} {selectedLead?.parent_last_name} -{' '}
            <Tag color="green">{selectedLead?.school_name}</Tag>
          </Title>
          {tab === 'info' && <Button onClick={handleEdit}>{isEditing ? 'Save' : 'Edit Lead'}</Button>}
        </Flex>
      }>
      <div>
        {isCallActive && !isSuccess && (
          <Card
            className="spacing-mb-16"
            title={
              <>
                <PhoneOutlined />
                <span className="spacing-ml-16">Calling to {selectedLead?.phone}</span>
              </>
            }>
            <Flex align="center" gap={16}>
              <SelectionCard
                Icon={PhoneOutlined}
                value="attempting-contact"
                label="Move to Attempting Contact Status"
                onClick={() => setStatus('attempting-contact')}
                selected={status}
              />
              <SelectionCard
                Icon={MessageOutlined}
                value="working"
                label="Move to Working"
                onClick={() => setStatus('working')}
                selected={status}
              />
            </Flex>
            <Flex justify="flex-end" className="spacing-mt-16">
              <Button type="primary" loading={loadingStatus} onClick={handleStatusUpdate}>
                Update to {status === 'attempting-contact' ? 'Attempting Contact' : 'Working'}
              </Button>
            </Flex>
          </Card>
        )}
        {isCallActive && isSuccess && (
          <Alert
            message={`Lead Moved to ${status === 'attempting-contact' ? 'Attempting Contact' : 'Working'}`}
            type="success"
          />
        )}
        <Tabs
          items={[
            {
              key: 'info',
              label: 'Lead Info',
            },
            {
              key: 'notes',
              label: 'Notes & History',
            },
          ]}
          onChange={setTab}
        />
        {tab === 'info' && (
          <>
            <Card
              className="spacing-mb-16"
              title={
                <Title level={5} style={{ margin: 0 }}>
                  Parent Info
                </Title>
              }>
              <Flex align="center" gap={16}>
                <InputField
                  id="parent_first_name"
                  label="Parent First Name"
                  value={formState.parent_first_name}
                  onChange={(e) => handleInputChange('parent_first_name', e.target.value)}
                  disabled={!isEditing}
                />
                <InputField
                  id="parent_last_name"
                  label="Parent Last Name"
                  value={formState.parent_last_name}
                  onChange={(e) => handleInputChange('parent_last_name', e.target.value)}
                  disabled={!isEditing}
                />
              </Flex>
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <label className="font-12 font-500 label-address">Address</label>
                  <GooglePlaces
                    aria-label="address"
                    initialValue={formState.address_description}
                    inputValue={formState.address_description}
                    onSelect={setLocationFields}
                    onChange={clearLocationFields}
                    disabled={!isEditing}
                    skipSuggest={(suggest) => {
                      return suggest.types.indexOf('geocode') === -1;
                    }}
                  />
                </Col>
                <Col xs={24} md={12}>
                  <InputField
                    id="city"
                    label="City"
                    value={formState.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    disabled={!isEditing}
                  />
                </Col>
              </Row>
              <Flex align="center" gap={16}>
                <InputField
                  id="state"
                  label="State"
                  value={formState.state}
                  onChange={(e) => handleInputChange('state', e.target.value)}
                  disabled={!isEditing}
                />
                <InputField
                  id="zipcode"
                  label="Zipcode"
                  value={formState.zipcode}
                  onChange={(e) => handleInputChange('zipcode', e.target.value)}
                  disabled={!isEditing}
                />
              </Flex>
              <Flex align="center" gap={16}>
                <InputField
                  id="email"
                  label="Email"
                  value={formState.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  disabled={!isEditing}
                />
                <InputField
                  id="phone"
                  label="Phone"
                  value={formState.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  disabled={!isEditing}
                />
              </Flex>
              <Flex align="center" gap={16}>
                <InputField id="language" label="Language">
                  <Select
                    style={{ width: '100%' }}
                    value={formState.language}
                    onChange={(value) => handleInputChange('language', value)}
                    options={languageOptions}
                    disabled={!isEditing}
                    uuid="language"
                  />
                </InputField>
              </Flex>
            </Card>

            <Card
              className="spacing-mb-16"
              title={
                <Title level={5} style={{ margin: 0 }}>
                  Student Info
                </Title>
              }>
              <Flex align="center" gap={16}>
                <InputField
                  id="child_first_name"
                  label="Student First Name"
                  value={formState.child_first_name}
                  onChange={(e) => handleInputChange('child_first_name', e.target.value)}
                  disabled={!isEditing}
                />
                <InputField
                  id="child_last_name"
                  label="Student Last Name"
                  value={formState.child_last_name}
                  onChange={(e) => handleInputChange('child_last_name', e.target.value)}
                  disabled={!isEditing}
                />
              </Flex>
              <Flex align="center" style={{ width: '50%', paddingRight: '0.5rem' }}>
                <InputField id="grade" label="Grade">
                  <Select
                    style={{ width: '100%' }}
                    value={formState.grade}
                    onChange={(value) => handleInputChange('grade', value)}
                    options={grades}
                    disabled={!isEditing}
                  />
                </InputField>
              </Flex>
              <Row gutter={[16, 16]} className="spacing-mb-16">
                <Col xs={24} md={12}>
                  {renderSelectLeadStages()}
                </Col>
                <Col xs={24} md={12}>
                  {renderSelectLeadSource()}
                </Col>
              </Row>
              <Row gutter={[16, 16]} className="spacing-mb-16">
                <Col xs={24} md={12}>
                  <ScholaCard className="spacing-p-8">
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <label className="spacing-mb-8">Application Received</label>
                      <Flex align="center" gap={8}>
                        <Checkbox
                          id="application_received"
                          checked={formState.application_received}
                          onChange={(e) => handleInputChange('application_received', e.target.checked)}
                          disabled={!isEditing}
                        />
                        <label className="form-check-label" htmlFor="application_received">
                          Received
                        </label>
                      </Flex>
                    </div>
                    {formState.application_received && (
                      <>
                        <div className="form-group radio-buttons spacing-mt-8">
                          <div
                            className="form-check pl-0 toggle-lead-valid"
                            style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                            <span>Invalid</span>
                            <Switch
                              checked={formState.application_valid === true}
                              disabled={!isEditing}
                              onChange={() =>
                                handleInputChange(
                                  'application_valid',
                                  formState.application_valid === undefined ? true : !formState.application_valid
                                )
                              }
                              id={'rm'}
                              className="rm-toggle"
                            />
                            <span>Valid</span>
                          </div>
                        </div>
                        <div className="lead-information-datepiker">
                          <label>Received On</label>
                          <DatePicker
                            className="lead-date-picker"
                            disabled={!isEditing}
                            defaultValue={
                              formState.application_received_at
                                ? dayjs(formState.application_received_at, 'YYYY/MM/DD')
                                : null
                            }
                            onChange={(date) =>
                              handleInputChange('application_received_at', date ? date.toISOString() : '')
                            }
                            placeholder="Select date"
                            disabledDate={(current) => current && current > moment().endOf('day')}
                          />
                        </div>
                      </>
                    )}
                  </ScholaCard>
                </Col>
                <Col xs={24} md={12}>
                  <ScholaCard className="spacing-p-8">
                    <div style={{ display: 'flex', flexDirection: 'column' }}>
                      <label className="spacing-mb-8">Enrollment Confirmed</label>
                      <Flex align="center" gap={8}>
                        <Checkbox
                          id="enrollment_confirmed"
                          checked={formState.enrollment_confirmed}
                          onChange={(e) => handleInputChange('enrollment_confirmed', e.target.checked)}
                          disabled={!isEditing}
                        />
                        <label className="form-check-label" htmlFor="enrollment_confirmed">
                          Confirmed
                        </label>
                      </Flex>
                    </div>
                    {formState.enrollment_confirmed && (
                      <>
                        <div className="lead-information-datepiker spacing-mt-16">
                          <label>Confirmed On</label>
                          <DatePicker
                            className="lead-date-picker"
                            disabled={!isEditing}
                            defaultValue={
                              formState.enrollment_confirmed_at
                                ? dayjs(formState.enrollment_confirmed_at, 'YYYY-MM-DD')
                                : null
                            }
                            onChange={(date) =>
                              handleInputChange('enrollment_confirmed_at', date ? date.toISOString() : '')
                            }
                            placeholder="Select dates"
                            disabledDate={(current) => current && current > moment().endOf('day')}
                          />
                        </div>
                      </>
                    )}
                  </ScholaCard>
                </Col>
              </Row>
            </Card>

            {schoolFields.length > 0 && (
              <Card title="Custom Values">
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {schoolFields.map((field, idx) => {
                    if (field.isBooleanValue) {
                      return (
                        <label
                          key={field.id}
                          htmlFor={`toggle-${field.id}`}
                          style={{ display: 'flex', alignItems: 'center', gap: '.5rem' }}>
                          <span>{field.display_name}</span>
                          <Switch
                            id={`toggle-${field.id}`}
                            checked={formState[field.field_indentifier]?.toLowerCase() === 'yes'}
                            checkedChildren="Yes"
                            unCheckedChildren="No"
                            onChange={() => {
                              const isAfirmative = formState[field.field_indentifier]?.toLowerCase() === 'yes';
                              setFormState((prev) => ({
                                ...prev,
                                [field.field_indentifier]: isAfirmative ? 'no' : 'yes',
                              }));
                            }}
                            disabled={!isEditing}
                          />
                        </label>
                      );
                    }
                    return (
                      <InputField
                        key={field.id}
                        id={`custom_field_${idx}`}
                        label={field.display_name}
                        value={formState[field.field_indentifier]}
                        onChange={(e) => handleInputChange(field.field_indentifier, e.target.value)}
                        disabled={!isEditing}
                      />
                    );
                  })}
                </div>
              </Card>
            )}
          </>
        )}

        {tab === 'notes' && (
          <>
            <div>
              <Card
                title={
                  <Title level={5} style={{ margin: 0 }}>
                    Add Note
                  </Title>
                }
                style={{ borderRadius: '8px' }}>
                <Input.TextArea
                  placeholder="Add your notes here..."
                  value={noteText}
                  onChange={(e) => setNoteText(e.target.value)}
                  style={{ marginBottom: '16px' }}
                />
                <Button type="primary" loading={loading} onClick={handleAddNote}>
                  Add Note
                </Button>
              </Card>
            </div>

            <div style={{ marginTop: '24px' }}>
              <Card
                title={
                  <Title level={5} style={{ margin: 0 }}>
                    Interaction History
                  </Title>
                }
                style={{ borderRadius: '8px' }}>
                <Space direction="vertical" style={{ width: '100%' }} size="large">
                  {!initialized || loading ? (
                    <Skeleton />
                  ) : fullNotes?.length > 0 ? (
                    fullNotes?.map((note) => (
                      <NoteCard
                        key={note.id || note.marketing_campaign_executed_audit_id}
                        note={note}
                        updateNote={updateNote}
                        removeNote={removeNote}
                      />
                    ))
                  ) : (
                    <Text>No Interaction Histoy Yet.</Text>
                  )}
                </Space>
              </Card>
            </div>
          </>
        )}
      </div>
    </Drawer>
  );
};

const InputField = ({ children, id, label, value, disabled, onChange }) => {
  return (
    <label htmlFor={id} style={{ display: 'block', width: '100%', marginBottom: '8px' }}>
      <Text>{label}</Text>
      {children || <Input id={id} name={id} value={value} onChange={onChange} disabled={disabled} />}
    </label>
  );
};

export const NoteCard = ({ note, removeNote, updateNote }) => {
  const [isEditing, setIsEditing] = useState(false);
  const inputRef = useRef(null);

  const [text, setText] = useState(note.note || '');

  const handleEdition = async () => {
    setIsEditing(false);
    await updateNote({ noteId: note.id, note: { note: text } });
  };

  const handleRemove = async () => {
    await removeNote(note.id);
  };

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      // Move cursor to end
      const val = inputRef.current.value;
      inputRef.current.value = '';
      inputRef.current.value = val;
    }
  }, [isEditing]);

  const handleCancel = () => {
    setText(note.note);
    setIsEditing(false);
  };

  const [deleteOpen, setDeleteOpen] = useState(false);

  const items = [
    {
      label: (
        <div onClick={() => setIsEditing(true)}>
          <EditOutlined style={{ marginRight: '.5rem' }} />
          Edit
        </div>
      ),
      key: 'edit',
    },
    {
      label: (
        <div onClick={setDeleteOpen}>
          <DeleteOutlined style={{ marginRight: '.5rem' }} />
          Remove
        </div>
      ),
      key: 'remove',
    },
  ];
  return (
    <>
      <div
        style={{
          display: 'flex',
          gap: '12px',
          background: '#f5f5f5',
          padding: '16px 24px 16px 16px',
          borderRadius: '8px',
          position: 'relative',
          border: isEditing ? '2px solid #c1d0ea' : '',
        }}>
        {!note.object_type ? (
          <EditOutlined style={{ color: '#006b8f' }} />
        ) : note?.type === 'sms' ? (
          <MessageOutlined />
        ) : (
          <MailOutlined />
        )}
        <div style={{ flex: 1 }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>
            <Text strong>{note.user_id || `Campaign: ${note.campaign}`}</Text>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {formatDate(note.created_at)}
            </Text>
          </div>
          {isEditing ? (
            <div>
              <textarea
                ref={inputRef}
                value={text}
                onChange={(e) => setText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    handleEdition();
                  }
                }}
                style={{
                  fontSize: '12px',
                  color: '#212121',
                  border: '1px solid #c1d0ea',
                  background: 'transparent',
                  width: '100%',
                  borderRadius: '4px',
                }}
              />
              <Flex align="center" justify="flex-end" gap={16} className="spacing-mt-16">
                <Button ghost type="primary" size="small" onClick={handleEdition}>
                  Update Note
                </Button>
                <Button type="text" size="small" onClick={handleCancel}>
                  Cancel
                </Button>
              </Flex>
            </div>
          ) : (
            <Text>{text || `Sent a ${note.type}`}</Text>
          )}
        </div>
        {!note?.marketing_campaign_id && (
          <div style={{ position: 'absolute', top: 8, right: 8 }}>
            <Dropdown menu={{ items }} trigger={['click']}>
              <MoreOutlined />
            </Dropdown>
          </div>
        )}
      </div>

      <Modal
        open={deleteOpen}
        title={
          <div>
            <ExclamationCircleFilled style={{ color: '#faad14' }} />
            <span style={{ marginLeft: '.5rem' }}>Are you sure you want to delete this note?</span>
          </div>
        }
        okText="Yes"
        okType="danger"
        cancelText="No"
        onOk={handleRemove}
        onCancel={() => setDeleteOpen(false)}
      />
    </>
  );
};
