import React, { useEffect, useState } from 'react';
import { DatePicker, Input, Button, Select, Checkbox, Modal, Segmented, Flex } from 'antd';
import styles from './advanced-filters.module.scss';
import { useSelector } from 'react-redux';
import { clearAdvancedFilters, setAdvancedFilters, toggleAdvancedFilter } from 'redux/spot/spot-actions';
import gradesArray from 'util/grades-array';
import { useLeadSources } from 'components/pages/crm-spot/hooks/useLeadSources';
import { useLeadSubStages } from 'components/pages/crm-spot/hooks/useLeadSubStages';

export const AdvancedFilters = ({ schoolId, schoolFields = [] }) => {
  const { isAdvancedFilterOpen, advancedFilters } = useSelector((state) => state.spot);

  // build initialFilters dinalically including custom fields
  const buildInitialFilters = () => {
    const base = {
      createdAt_from: '',
      createdAt_to: '',
      grades: [],
      search_notes: '',
      year: null,
      language: null,
      viewed_leads: '',
      lead_source_id: null,
      lead_status_id: null,
    };
    if (schoolFields && schoolFields.length > 0) {
      schoolFields.forEach((field) => {
        base[field.field_indentifier] = '';
      });
    }
    return base;
  };

  const initialFilters = buildInitialFilters();

  const [filters, setFilters] = useState(initialFilters);

  // update filters state when schoolFields have data
  useEffect(() => {
    const newInitialFilters = buildInitialFilters();

    if (advancedFilters && Object.keys(advancedFilters).length > 0) {
      setFilters({ ...newInitialFilters, ...advancedFilters });
    } else {
      setFilters(newInitialFilters);
    }
  }, [schoolFields, advancedFilters]);

  const { sources } = useLeadSources(schoolId);
  const { statuses: subStages } = useLeadSubStages(schoolId);

  const handleFilter = (key, value) => {
    setFilters((prevFilters) => {
      const currentValue = prevFilters[key];
      const isArray = Array.isArray(currentValue);

      if (!isArray) {
        return {
          ...prevFilters,
          [key]: value,
        };
      }

      const updatedArray = currentValue.includes(value)
        ? currentValue.filter((item) => item !== value)
        : [...currentValue, value];

      return {
        ...prevFilters,
        [key]: updatedArray,
      };
    });
  };

  // Generate dynamic school years starting from 2023-2024 up to 3 years ahead from current year
  const getOptionYears = () => {
    const startYear = 2020;
    const currentYear = new Date().getFullYear();
    const years = [];
    // Always show at least from 2023-2024 up to current year + 3
    const endYear = Math.max(currentYear + 4, startYear + 4);
    for (let y = startYear; y <= endYear; y++) {
      years.push({ value: `${y}-${y + 1}`, label: `${y}-${y + 1}` });
    }
    return years;
  };
  const optionYears = getOptionYears();

  const onResetFilters = () => {
    clearAdvancedFilters();
    setFilters(initialFilters);
    toggleAdvancedFilter();
  };

  const onApplyFilters = () => {
    setAdvancedFilters(filters);
    toggleAdvancedFilter();
  };

  return (
    <Modal
      title="Filters"
      open={isAdvancedFilterOpen}
      onCancel={toggleAdvancedFilter}
      footer={[
        <Button key="reset" onClick={onResetFilters}>
          Reset Filters
        </Button>,
        <Button key="apply" onClick={onApplyFilters} type="primary">
          Apply Filters
        </Button>,
      ]}>
      <div className={styles.advancedFiltersContainer}>
        <Flex gap={16} align="center">
          <label className={styles.filterLabel} style={{ marginBottom: 0 }}>
            View Status
          </label>
          <Segmented
            options={[
              { label: 'All', value: '' },
              { label: 'Viewed', value: 'viewed' },
              { label: 'Not Viewed', value: 'not-viewed' },
            ]}
            value={filters.viewed_leads}
            onChange={(value) => handleFilter('viewed_leads', value)}
          />
        </Flex>
        <div>
          <label className={styles.filterLabel}>Grades</label>
          <div className={styles.filterGrid}>
            {gradesArray.map((grade) => {
              let label;
              if (grade === 'Pre-K' || grade === 'TK' || grade === 'HS' || grade === 'K') {
                label = grade;
              } else {
                // Add ordinal suffixes for 1st, 2nd, 3rd, 4th, etc.
                const n = parseInt(grade, 10);
                if (!isNaN(n)) {
                  let suffix = 'th';
                  if (n === 1) suffix = 'st';
                  else if (n === 2) suffix = 'nd';
                  else if (n === 3) suffix = 'rd';
                  else if (n > 3 && n < 21) suffix = 'th';
                  else if (n % 10 === 1) suffix = 'st';
                  else if (n % 10 === 2) suffix = 'nd';
                  else if (n % 10 === 3) suffix = 'rd';
                  label = `${n}${suffix} Grade`;
                } else {
                  label = grade;
                }
              }
              return (
                <div key={grade} className={styles.filterCheckbox}>
                  <Checkbox
                    id={`grade-${grade}`}
                    checked={filters.grades.includes(grade)}
                    onChange={() => handleFilter('grades', grade)}>
                    {label}
                  </Checkbox>
                </div>
              );
            })}
          </div>
        </div>

        {schoolFields.length > 0 && (
          <div className={styles.filterGroup}>
            {schoolFields.map((field) => (
              <div key={field.field_indentifier}>
                <label htmlFor={field.field_indentifier} className={styles.filterLabel}>
                  {field.display_name} {field.isBooleanValue && '(Yes/No)'}
                </label>
                {!field.isBooleanValue ? (
                  <Input
                    id={field.field_indentifier}
                    value={filters[field.field_indentifier] || ''}
                    onChange={(e) => handleFilter(field.field_indentifier, e.target.value)}
                  />
                ) : (
                  <Select
                    id={field.field_indentifier}
                    placeholder="Select value"
                    className={styles.filterSelect}
                    options={[
                      { label: 'Yes', value: 'yes' },
                      { label: 'No', value: 'no' },
                    ]}
                    value={filters[field.field_indentifier]}
                    onChange={(value) => handleFilter(field.field_indentifier, value)}
                    allowClear
                  />
                )}
              </div>
            ))}
          </div>
        )}

        <div className={styles.filterGroup}>
          <div>
            <label htmlFor="year" className={styles.filterLabel}>
              Year Added
            </label>
            <Select
              id="year"
              placeholder="Select Year"
              value={filters.year}
              onChange={(value) => handleFilter('year', value)}
              className={styles.filterSelect}
              options={optionYears}
              allowClear
            />
          </div>

          {schoolId && (
            <>
              <div>
                <label htmlFor="lead_source_id" className={styles.filterLabel}>
                  Source
                </label>
                <Select
                  id="lead_source_id"
                  value={filters.lead_source_id}
                  onChange={(value) => handleFilter('lead_source_id', value)}
                  className={styles.filterSelect}
                  options={
                    sources?.map((source) => ({
                      value: source.id,
                      label: source.name,
                    })) || []
                  }
                  placeholder="Select Lead Source"
                  allowClear
                />
              </div>
              <div>
                <label htmlFor="lead_status_id" className={styles.filterLabel}>
                  Sub Stage
                </label>
                <Select
                  id="lead_status_id"
                  placeholder="Select Lead Sub Stage"
                  value={filters.lead_status_id}
                  onChange={(value) => handleFilter('lead_status_id', value)}
                  className={styles.filterSelect}
                  options={
                    subStages?.map((stage) => ({
                      value: stage.id,
                      label: stage.name,
                    })) || []
                  }
                  allowClear
                />
              </div>
            </>
          )}

          <div>
            <label htmlFor="language" className={styles.filterLabel}>
              Language
            </label>
            <Select
              id="language"
              placeholder="Select Language"
              value={filters.language}
              onChange={(value) => handleFilter('language', value)}
              className={styles.filterSelect}
              options={[
                { value: 'english', label: 'English' },
                { value: 'spanish', label: 'Spanish' },
              ]}
              allowClear
            />
          </div>
        </div>

        <div>
          <label className={styles.filterLabel}>Date Added</label>
          <div className={styles.filterRow}>
            <div className={styles.filterItem}>
              <label htmlFor="dateFrom" className={styles.filterSubLabel}>
                Date Added From
              </label>
              <DatePicker id="dateFrom" onChange={(date, dateString) => handleFilter('createdAt_from', dateString)} />
            </div>
            <div className={styles.filterItem}>
              <label htmlFor="dateTo" className={styles.filterSubLabel}>
                Date Added To
              </label>
              <DatePicker id="dateTo" onChange={(date, dateString) => handleFilter('createdAt_to', dateString)} />
            </div>
          </div>
        </div>

        <div>
          <label htmlFor="search_notes" className={styles.filterLabel}>
            Search in Notes
          </label>
          <Input.TextArea
            id="search_notes"
            placeholder="Search for keywords in lead notes..."
            value={filters.search_notes}
            onChange={(e) => handleFilter('search_notes', e.target.value)}
            className={styles.filterTextarea}
          />
        </div>
      </div>
    </Modal>
  );
};
