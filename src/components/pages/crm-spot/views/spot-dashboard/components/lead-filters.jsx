import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import { Flex, Input, Button, Tabs, Badge, Select } from 'antd';
import { SearchOutlined, FilterOutlined, PlusOutlined, SettingOutlined } from '@ant-design/icons';
import { isSameDay } from 'date-fns';
import { toggleAdvancedFilter } from 'redux/spot/spot-actions';
import styles from './lead-filters.module.scss';
/**
 * @typedef {Object} LeadFiltersProps
 * @property {string} filterValue - The current search filter value
 * @property {function} handleFilterSearch - Function to handle changes to the search filter
 * @property {string} statusFilter - The currently selected status filter tab
 * @property {function} handleTabStatus - Function to update the status filter
 */

/**
 * LeadFilters component for the SPOT dashboard
 *
 * This component provides filtering functionality for leads in the SPOT dashboard.
 * It includes a search input, buttons for adding leads and advanced filtering,
 * and tabs for filtering leads by status.
 *
 * @param {LeadFiltersProps} props - Component props
 * @returns {JSX.Element} The rendered LeadFilters component
 */
export const LeadFilters = ({
  filterValue,
  handleFilterSearch,
  statusFilter,
  handleTabStatus,
  isAssignedMode,
  schoolsByUser,
  schoolId,
  onSelectSchool,
  toggleSettings,
  statusCounts,
  users,
  userId,
  selectedUserId,
  onChangeSelectedUserId,
}) => {
  /**
   * Configuration for the status filter tabs
   *
   * @type {Array<{key: string, label: string|JSX.Element}>}
   */
  const items = useMemo(() => {
    const getLabelCount = (count) => count ?? 0;
    let baseItems = [
      {
        key: 'new',
        label: (
          <BadgeLabel label="New" count={getLabelCount(statusCounts.new)} value="new" statusFilter={statusFilter} />
        ),
      },
      {
        key: 'attempting-contact',
        label: (
          <BadgeLabel
            label="Attempting Contact"
            count={getLabelCount(statusCounts['attempting-contact'])}
            value="attempting-contact"
            statusFilter={statusFilter}
          />
        ),
      },
      {
        key: 'working',
        label: (
          <BadgeLabel
            label="Working"
            count={getLabelCount(statusCounts.working)}
            value="working"
            statusFilter={statusFilter}
          />
        ),
      },
      // 'matched' will be conditionally included
      {
        key: 'application-sent',
        label: (
          <BadgeLabel
            label="Application Sent"
            count={getLabelCount(statusCounts['application-sent'])}
            value="application-sent"
            statusFilter={statusFilter}
          />
        ),
      },
      {
        key: 'archived',
        label: (
          <BadgeLabel
            label="Archived"
            count={getLabelCount(statusCounts.archived)}
            value="archived"
            statusFilter={statusFilter}
          />
        ),
      },
      {
        key: 'tasks',
        label: <span>Tasks</span>,
      },
    ];

    if (!isAssignedMode) {
      // Insert 'matched' after 'working' if not assigned mode
      baseItems.splice(3, 0, {
        key: 'matched',
        label: (
          <BadgeLabel
            label="Matched"
            count={getLabelCount(statusCounts.matched)}
            value="matched"
            statusFilter={statusFilter}
          />
        ),
      });
      return baseItems;
    }
    // In assigned mode, add extra tabs and hide 'matched'
    // Insert after 'application-sent'
    const insertIdx = baseItems.findIndex((item) => item.key === 'application-sent') + 1;
    baseItems.splice(
      insertIdx,
      0,
      {
        key: 'application-received',
        label: (
          <BadgeLabel
            label="Application Received"
            count={getLabelCount(statusCounts['application-received'])}
            value="application-received"
            statusFilter={statusFilter}
          />
        ),
      },
      {
        key: 'accepted',
        label: (
          <BadgeLabel
            label="Accepted"
            count={getLabelCount(statusCounts.accepted)}
            value="accepted"
            statusFilter={statusFilter}
          />
        ),
      },
      {
        key: 'waitlisted',
        label: (
          <BadgeLabel
            label="Waitlist"
            count={getLabelCount(statusCounts.waitlisted)}
            value="waitlisted"
            statusFilter={statusFilter}
          />
        ),
      }
    );
    return baseItems;
  }, [isAssignedMode, statusCounts]);

  const options = useMemo(() => {
    if (!schoolsByUser.length === 0) return [];

    const schoolOptions = schoolsByUser?.map((school) => ({
      label: (
        <span className={school.school_assigned ? undefined : styles.cursive}>
          {school.name}{' '}
          {school.school_assigned ? '' : <PlusOutlined style={{ fontSize: '8px', color: 'rgb(29, 78, 216)' }} />}
        </span>
      ),
      value: school.school_id,
    }));

    return [{ label: 'All Schools', value: '' }, ...schoolOptions];
  }, [schoolsByUser]);

  const optionsUsers = useMemo(() => {
    if (!users.length === 0) return [];

    const usersOptions = users
      ?.filter((user) => user.user_id !== userId)
      .sort((a, b) => {
        const nameA = `${a.first_name || ''} ${a.last_name || ''}`.trim().toLowerCase();
        const nameB = `${b.first_name || ''} ${b.last_name || ''}`.trim().toLowerCase();
        return nameA.localeCompare(nameB);
      })
      .map((user) => ({
        label: (
          <span>
            {`Assigned to ${user.first_name && user.last_name ? `${user.first_name} ${user.last_name}` : user.email}`}
          </span>
        ),
        value: user.user_id,
      }));

    return [{ label: 'All Users', value: '' }, { label: 'Assigned to me', value: userId }, ...usersOptions];
  }, [users]);

  return (
    <div className={styles.container}>
      <Flex justify="end" align="flex-end" gap={16}>
        <div style={{ flex: 1 }}>
          <Input
            placeholder={statusFilter === 'tasks' ? 'Search tasks...' : 'Search leads...'}
            prefix={<SearchOutlined />}
            value={filterValue}
            onChange={(e) => handleFilterSearch(e.target.value)}
          />
        </div>
        {/* <div>
          <Flex gap="middle" align="flex-end">
            <Button size="big" icon={<PlusOutlined />} type="primary" onClick={toggleNewLead}>
              Add Lead
            </Button>
            <Button icon={<FilterOutlined />} type="primary" onClick={toggleAdvancedFilter}>
              Filters
            </Button>
          </Flex>
        </div> */}
        {isAssignedMode && optionsUsers.length > 0 && (
          <Select
            defaultValue={''}
            value={selectedUserId}
            onChange={onChangeSelectedUserId}
            options={optionsUsers}
            style={{ minWidth: '300px', height: 'auto' }}
          />
        )}
        {isAssignedMode && options.length > 0 && (
          <Select
            defaultValue={''}
            value={schoolId}
            onChange={onSelectSchool}
            options={options}
            style={{ minWidth: '300px', height: 'auto' }}
          />
        )}
        <Button icon={<FilterOutlined />} onClick={toggleAdvancedFilter}>
          Filters
        </Button>
        <Button icon={<SettingOutlined />} onClick={toggleSettings} />
      </Flex>

      <Flex className="spacing-my-16 border-bottom-gray" align="flex-end">
        <Tabs
          activeKey={statusFilter}
          animated
          onChange={handleTabStatus}
          className="tabs-lead-filters"
          items={items}
          tabBarGutter={16}
        />
      </Flex>
    </div>
  );
};

const BadgeLabel = ({ label, count, value, statusFilter }) => {
  // Format number if is greater or equal to 1000
  const getDisplayCount = (count) => {
    if (count >= 1000) {
      return `${(count / 1000).toFixed(count % 1000 === 0 ? 0 : 2)}K`;
    }
    return count;
  };

  return (
    <Flex align="center" gap=".5rem">
      {label}
      <Badge
        showZero
        overflowCount={999}
        count={getDisplayCount(count)}
        color={statusFilter === value ? '#016b8f' : '#f5f5f5'}
        style={{ color: statusFilter === value ? '#fff' : '#6c6d70' }}
      />
    </Flex>
  );
};

/**
 * PropTypes for the LeadFilters component
 */
LeadFilters.propTypes = {
  /** The current search filter value */
  filterValue: PropTypes.string,
  /** Function to handle changes to the search filter */
  handleFilterSearch: PropTypes.func,
  /** The currently selected status filter tab */
  statusFilter: PropTypes.string,
  /** Function to update the status filter */
  handleTabStatus: PropTypes.func,
};
