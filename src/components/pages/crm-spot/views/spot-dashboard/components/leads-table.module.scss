.row {
  & td {
    vertical-align: middle;
  }
}

.selectedRow {
  background-color: #f9fdff !important;
}
.unviewed {
  background: #e7f0f9;
}
.incomplete {
  border: 1px solid #e5e7eb;
  background: #f9fafb;
  color: #4b5563;
  border-radius: 0.5rem;
  padding: 0.5rem;
}

.schoolLabel {
  background: #dcfce7;
  color: #15803d;
  border-radius: 0.5rem;
  cursor: pointer;
}

.drawerHeader {
  display: none !important;
}
.drawerBody {
  padding: 0 !important;
}

.selectionContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background-color: #ebf8ff;
  border: 1px solid #bee3f8;
  border-radius: 0.5rem;
}

.selectionText {
  font-size: 1rem;
  font-weight: 500;
  color: #2b6cb0;
}

.selectionActions {
  display: flex;
  gap: 0.5rem;
}

.clearButton {
  border-color: #90cdf4;
  color: #2b6cb0;

  &:hover {
    background-color: #bee3f8;
  }
}

.updateButton {
  background-color: #00506c;
  color: white;

  &:hover {
    background-color: #003d52;
  }
}

.cellStyle {
  text-align: center;
  border: 1px solid #d9d9d9;
  border-radius: 0.25rem;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  max-width: 285px;
}

.buttonStyle {
  font-size: 14px;
  height: 1.75rem;
  width: 1.75rem;
  border-radius: 0.5rem;
  border: 1px solid #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
}
.leadsTable {
  :global(table > thead > tr:first-child > *:first-child) {
    border-start-start-radius: 0.5rem !important;
  }
  :global(table > thead > tr:first-child > *:last-child) {
    border-start-end-radius: 0.5rem !important;
  }
  th {
    vertical-align: middle;
    text-align: center !important;
  }
  label {
    margin-bottom: 0 !important;
  }
}

.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1rem 0;
}
