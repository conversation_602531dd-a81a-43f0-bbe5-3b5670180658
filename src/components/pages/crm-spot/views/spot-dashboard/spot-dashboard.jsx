import React, { useState } from 'react';
import { Flex, Segmented, Button } from 'antd';
import { LeadHeader } from '../../shared-components';
import { useSelector } from 'react-redux';
import {
  clearSelectedLead,
  setAssignedMode,
  setFilterValue,
  setGlobalValue,
  setStatusFilter,
} from 'redux/spot/spot-actions';
import {
  LeadFilters,
  LeadsTableContent,
  AddLeadForm,
  UpdateStage,
  AdvancedFilters,
  TaskTable,
  LeadTaskModal,
  LeadEdition,
  LeadHistoryModal,
  SendAppModal,
  GlobalSearchModal,
} from './components';
import { _getLeads, _updateLeadBatch } from 'controllers/leads/leads_controller';
import styles from './spot-dashboard.module.scss';
import { getProfile } from 'util/auth';
import { useUserTasks } from '../../hooks/useUserTasks';
import { useLeadAssignments } from '../../hooks/useAssignmentsLeads';
import { _getUserLeadAssignments } from 'controllers/lead-assignments/lead-assignments-controller';
import { HeaderTitleButtons } from '../../shared-components/header-title-buttons';
import { useToggle } from 'hooks/useToggle';
import { useNotes } from '../../hooks/useNotes';
import { useSchoolsAssigned } from '../../hooks/useSchoolsAssigned';
import { useNotification } from '../../layouts/crm-spot-layout';
import { useDebounce } from 'hooks/useDebounce';
import { useSchoolFields } from '../../hooks/useSchoolFields';
import { useSPOTUsers } from '../../hooks/useSPOTUsers';
import { UserOutlined } from '@ant-design/icons';
import { useUpdateLead } from '../../hooks/useUpdateLead';

export const SPOTDashboard = () => {
  const { selectedLead, statusFilter, schoolId, isLeadEdition, filterValue, advancedFilters, isAssignedMode, tasks } =
    useSelector((state) => state.spot);
  const userId = getProfile().sub;

  const { openNotification } = useNotification();

  const { updateUserTaskStatus, createUserTask } = useUserTasks(userId);

  const [sorter, setSorter] = useState({ field: 'created_at', order: null });

  const { schoolsByUser, onSelectSchool } = useSchoolsAssigned({ user_id: userId });

  const textSearch = useDebounce(filterValue, 500);

  const { users } = useSPOTUsers();

  const [selectedUserId, setSelectedUserId] = useState('');

  const {
    data: rawLeads,
    statusCounts,
    loading,
    updateAssignment,
    getLeadAssignments,
    handleSentApp,
    currentPage,
    pageSize,
    handlePageChange,
    handlePageSizeChange,
  } = useLeadAssignments({
    user_id: userId,
    assigned_user_id: selectedUserId,
    status: statusFilter === 'tasks' ? '' : statusFilter,
    createdAt_from: advancedFilters.createdAt_from || '',
    createdAt_to: advancedFilters.createdAt_to || '',
    grades: advancedFilters.grades,
    search_notes: advancedFilters.search_notes || '',
    year: advancedFilters.year || '',
    language: advancedFilters.language || '',
    lead_source_id: schoolId ? advancedFilters.lead_source_id : null,
    lead_status_id: schoolId ? advancedFilters.lead_status_id : null,
    assigned_schools: isAssignedMode,
    school_id: schoolId,
    textSearch,
    viewed_leads: advancedFilters.viewed_leads,
    custom_field_1: schoolId ? advancedFilters.custom_field_1 : null,
    custom_field_2: schoolId ? advancedFilters.custom_field_2 : null,
    custom_field_3: schoolId ? advancedFilters.custom_field_3 : null,
    custom_field_4: schoolId ? advancedFilters.custom_field_4 : null,
    custom_field_5: schoolId ? advancedFilters.custom_field_5 : null,
    fieldSort: sorter.field,
    fieldDirection: sorter.order,
  });

  const { schoolFields } = useSchoolFields(schoolId);

  const [isApplicationOpen, toggleApplication] = useToggle(false);
  const [isCallActive, toggleCalling] = useToggle(false);

  const { updateLead } = useUpdateLead();
  const markLeadAsViewed = async ({ schoolId, leadId }) => {
    const viewed_at = new Date();
    try {
      const res = await updateLead({ schoolId, leadIds: [leadId], values: { viewed_at } });
      if (res) {
        getLeadAssignments();
      }
    } catch (error) {
      console.log('markLeadAsViewed', error);
    }
  };

  const onSendApp = async ({ lead, sendToEmail }) => {
    try {
      const resApp = await handleSentApp({ schoolId: lead.school_id, leadId: lead.id, userId, sendToEmail });
      if (resApp) {
        if (!sendToEmail) {
          await navigator.clipboard.writeText(resApp);
        }
        openNotification({
          message: `Application ${sendToEmail ? 'Sent' : 'Copied'}`,
          description: `${lead.parent_first_name} ${lead.parent_last_name}'s application has been ${
            sendToEmail ? 'sent and moved.' : 'copied to your clipboard.'
          }`,
        });
        toggleApplication();
      }
    } catch (error) {
      console.log('onSendApp', error);
    }
  };

  const handleFilterSearch = (value) => {
    setFilterValue(value);
  };

  const [isHistoryModalOpen, toggleHistoryModalOpen] = useToggle(false);
  const notes = useNotes(selectedLead);

  const [isSettingsOpen, toggleSettings] = useToggle(false);
  const [isGlobalSearchOpen, toggleGlobalSearch] = useToggle(false);

  const handleSelectLead = (lead) => {
    setGlobalValue('selectedLead', lead);
    setGlobalValue('isLeadEdition', true);
  };

  return (
    <div>
      <HeaderTitleButtons />
      <div className={styles.container}>
        <div className={styles.contentSection}>
          <LeadHeader user_id={userId} />
          <Flex justify="flex-end" align="center" gap={16}>
            <Button type="default" icon={<UserOutlined />} onClick={toggleGlobalSearch}>
              Global Search
            </Button>
            <Segmented
              className={styles.mode}
              size="large"
              options={['General Leads', 'Assigned School Leads']}
              value={isAssignedMode ? 'Assigned School Leads' : 'General Leads'}
              onChange={setAssignedMode}
            />
          </Flex>
          <LeadFilters
            isAssignedMode={isAssignedMode}
            statusFilter={statusFilter}
            handleFilterSearch={handleFilterSearch}
            handleTabStatus={setStatusFilter}
            schoolsByUser={schoolsByUser}
            schoolId={schoolId}
            onSelectSchool={onSelectSchool}
            toggleSettings={toggleSettings}
            statusCounts={statusCounts}
            users={users}
            userId={userId}
            selectedUserId={selectedUserId}
            onChangeSelectedUserId={setSelectedUserId}
          />
          {statusFilter !== 'tasks' ? (
            <LeadsTableContent
              userId={userId}
              leads={rawLeads?.result || []}
              totalLeads={rawLeads?.totalCount || 0}
              loading={loading}
              schoolFields={schoolFields}
              handlePageChange={handlePageChange}
              handlePageSizeChange={handlePageSizeChange}
              pageSize={pageSize}
              currentPage={currentPage}
              toggleHistoryModalOpen={toggleHistoryModalOpen}
              updateAssignment={updateAssignment}
              isSettingsOpen={isSettingsOpen}
              toggleSettings={toggleSettings}
              setSorter={setSorter}
              toggleApplication={toggleApplication}
              toggleCalling={toggleCalling}
              markLeadAsViewed={markLeadAsViewed}
            />
          ) : (
            <TaskTable tasks={tasks} updateUserTaskStatus={updateUserTaskStatus} />
          )}
        </div>
      </div>
      {/* Modals layer: */}
      <AdvancedFilters schoolId={schoolId} schoolFields={schoolFields} />

      <LeadTaskModal tasks={tasks} createUserTask={createUserTask} updateUserTaskStatus={updateUserTaskStatus} />

      <AddLeadForm />

      <LeadEdition
        open={isLeadEdition}
        onClose={() => {
          setGlobalValue('isLeadEdition', false);
          clearSelectedLead();
          isCallActive && toggleCalling();
        }}
        getLeadAssignments={getLeadAssignments}
        notesHook={notes}
        userId={userId}
        isCallActive={isCallActive}
        updateAssignment={updateAssignment}
        schoolFields={schoolFields}
        loading={loading}
      />

      <LeadHistoryModal notesHook={notes} open={isHistoryModalOpen} onClose={toggleHistoryModalOpen} />

      <SendAppModal onSendApp={onSendApp} isApplicationOpen={isApplicationOpen} toggleApplication={toggleApplication} />

      <GlobalSearchModal
        open={isGlobalSearchOpen}
        onClose={toggleGlobalSearch}
        onSelectLead={handleSelectLead}
        userId={userId}
      />

      <UpdateStage
        assignments={{
          getLeadAssignments,
          updateAssignment,
          loading,
        }}
        userId={userId}
        notesHook={notes}
      />
    </div>
  );
};
