import { useState, useEffect } from 'react';
import { _getSchoolsAssigned } from 'controllers/lead-assignments/lead-assignments-controller';
import { clearAdvancedFilters, setGlobalValue } from 'redux/spot/spot-actions';

export const useSchoolsAssigned = ({ user_id }) => {
  const [schoolsByUser, setSchoolsByUser] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const onSelectSchool = (id) => {
    setGlobalValue('schoolId', id);

    if (!id) {
      clearAdvancedFilters();
    }
  };

  const fetchSchoolsAssigned = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await _getSchoolsAssigned(user_id);
      const data = await response.json();

      setGlobalValue(
        'schoolsOwned',
        data.map((school) => school.school_id)
      );

      setSchoolsByUser(data || []);
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSchoolsAssigned();
  }, []);

  return {
    schoolsByUser,
    loading,
    error,
    onSelectSchool,
  };
};
