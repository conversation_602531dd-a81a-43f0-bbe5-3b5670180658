import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  _getAssignmentsCount,
  _getUserLeadAssignments,
  _updateSubStatus,
  _getSchoolsAssigned,
  _createAssignLead,
  _getCountAssignments,
} from 'controllers/lead-assignments/lead-assignments-controller';
import { useUpdateLead } from './useUpdateLead';
import { _sendApplications } from 'controllers/leads/leads_controller';
import { setAdvancedFilters, setAssignedMode, setGlobalValue, setStatusFilter } from 'redux/spot/spot-actions';

/**
 * @typedef {Object} LeadAssignmentsParams
 * @property {string} user_id
 * @property {string} assigned_user_id
 * @property {string} status
 * @property {string} createdAt_from
 * @property {string} createdAt_to
 * @property {Array<string>} grades
 * @property {string} search_notes
 * @property {boolean} assigned_schools
 */
export const useLeadAssignments = ({
  user_id,
  assigned_user_id,
  status,
  createdAt_from,
  createdAt_to,
  grades,
  search_notes,
  assigned_schools,
  school_id,
  year,
  language,
  lead_source_id,
  lead_status_id,
  textSearch,
  viewed_leads,
  custom_field_1,
  custom_field_2,
  custom_field_3,
  custom_field_4,
  custom_field_5,
  fieldSort,
  fieldDirection,
}) => {
  const [isInitialized, setIsInitialized] = useState(false);

  // LOAD localstorage filters
  useEffect(() => {
    const initial = getInitialFilters();

    initial.assigned_schools && setAssignedMode();
    initial.statusFilter && setStatusFilter(initial.statusFilter);
    initial.school_id && setGlobalValue('schoolId', initial.school_id);
    const {
      grades,
      year,
      createdAt_from,
      createdAt_to,
      language,
      lead_source_id,
      lead_status_id,
      viewed_leads,
      custom_field_1,
      custom_field_2,
      custom_field_3,
      custom_field_4,
      custom_field_5,
    } = initial;

    if (
      grades ||
      year ||
      createdAt_from ||
      createdAt_to ||
      language ||
      viewed_leads ||
      lead_source_id ||
      lead_status_id ||
      custom_field_1 ||
      custom_field_2 ||
      custom_field_3 ||
      custom_field_4 ||
      custom_field_5
    ) {
      setAdvancedFilters({
        grades,
        year,
        createdAt_from,
        createdAt_to,
        language,
        lead_source_id,
        lead_status_id,
        viewed_leads,
        custom_field_1,
        custom_field_2,
        custom_field_3,
        custom_field_4,
        custom_field_5,
      });
    }
    setIsInitialized(true);
  }, []);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);

  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    if (size !== pageSize) setPageSize(size);
  };

  const handlePageSizeChange = (size) => {
    setPageSize(size);
  };

  // Reset current page when any filter changes
  useEffect(() => {
    setCurrentPage(1);
  }, [
    status,
    assigned_user_id,
    textSearch,
    pageSize,
    createdAt_from,
    createdAt_to,
    grades,
    search_notes,
    assigned_schools,
    school_id,
    year,
    language,
    viewed_leads,
    lead_source_id,
    lead_status_id,
    custom_field_1,
    custom_field_2,
    custom_field_3,
    custom_field_4,
    custom_field_5,
  ]);

  // SAVE filters in local storage;
  useEffect(() => {
    const FILTERS_KEY = 'assignmentsSearch';
    try {
      const stored = localStorage.getItem(FILTERS_KEY);
      const filters = stored ? JSON.parse(stored) : {};

      filters.assigned_schools = assigned_schools;
      filters.statusFilter = status;
      filters.school_id = school_id;
      filters.createdAt_from = createdAt_from;
      filters.createdAt_to = createdAt_to;
      filters.grades = grades;
      filters.year = year;
      filters.language = language;
      filters.lead_source_id = lead_source_id;
      filters.lead_status_id = lead_status_id;
      filters.viewed_leads = viewed_leads;
      filters.custom_field_1 = custom_field_1;
      filters.custom_field_2 = custom_field_2;
      filters.custom_field_3 = custom_field_3;
      filters.custom_field_4 = custom_field_4;
      filters.custom_field_5 = custom_field_5;

      localStorage.setItem(FILTERS_KEY, JSON.stringify(filters));
    } catch (e) {
      console.error('Error saving filters in localStorage:', e);
    }
  }, [
    assigned_schools,
    status,
    school_id,
    grades,
    year,
    language,
    createdAt_from,
    createdAt_to,
    lead_source_id,
    lead_status_id,
    viewed_leads,
    custom_field_1,
    custom_field_2,
    custom_field_3,
    custom_field_4,
    custom_field_5,
  ]);

  const [data, setData] = useState({});
  const [statusCounts, setStatusCounts] = useState({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchLeadAssignments = async () => {
    if (status === '') return;

    setLoading(true);
    setError(null);
    try {
      const response = await _getUserLeadAssignments({
        user_id,
        assigned_user_id,
        status,
        createdAt_from,
        createdAt_to,
        grades,
        search_notes,
        assigned_schools,
        school_id,
        year,
        language,
        lead_source_id,
        lead_status_id,
        textSearch,
        viewed_leads,
        custom_field_1,
        custom_field_2,
        custom_field_3,
        custom_field_4,
        custom_field_5,
        page: currentPage,
        pageSize,
        fieldSort,
        fieldDirection,
      });
      const data = await response.json();
      setData(data || {});
    } catch (err) {
      setError(err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusCounts = async () => {
    try {
      const res = await _getCountAssignments({
        user_id,
        createdAt_from,
        createdAt_to,
        grades,
        search_notes,
        assigned_schools,
        school_id,
        year,
        language,
        lead_source_id,
        lead_status_id,
        textSearch,
        assigned_user_id,
        viewed_leads,
        custom_field_1,
        custom_field_2,
        custom_field_3,
        custom_field_4,
        custom_field_5,
      });
      const dataCounts = await res.json();
      setStatusCounts(dataCounts);
    } catch (err) {
      console.log('fetchStatusCounts');
      setError(err);
    }
  };

  // Refetch assignments when filters change
  useEffect(() => {
    if (!isInitialized) return; // not make call until is initialized

    fetchLeadAssignments();
    getStatusCounts();
  }, [
    isInitialized,
    status,
    assigned_user_id,
    createdAt_from,
    createdAt_to,
    grades,
    search_notes,
    assigned_schools,
    school_id,
    year,
    language,
    lead_source_id,
    lead_status_id,
    textSearch,
    viewed_leads,
    custom_field_1,
    custom_field_2,
    custom_field_3,
    custom_field_4,
    custom_field_5,
    currentPage,
    pageSize,
    fieldSort,
    fieldDirection,
  ]);

  const { selectedLead, selectedLeads } = useSelector((state) => state.spot);
  const { updateLead, loading: leadLoading } = useUpdateLead();

  const updateAssignment = async ({ user_id, newValue, archivedReason, acceptedYear }) => {
    setLoading(true);
    setError(null);
    const leadIds = selectedLeads.length > 0 ? selectedLeads : [selectedLead];

    // const isSubStatusUpdate = ['attempting-contact', 'working', 'matched'].includes(newValue);
    const leadsGroupedBySchool = getGroupedLeads(leadIds);

    try {
      switch (newValue) {
        case 'new':
          console.log('STATUS: new, sub_status: empty');

          // update assignment
          for (const lead of leadIds) {
            if (!lead.lead_assignment_id) {
              await _createAssignLead({
                assignedUserId: user_id,
                leadId: lead.id,
                subStatus: '',
                assignmentType: 'school_assigned',
              });
            } else {
              await _updateSubStatus({
                user_id,
                assignment_id: lead.lead_assignment_id,
                sub_status: '',
              });
            }
          }
          // update lead
          for (const school of leadsGroupedBySchool) {
            await updateLead({ schoolId: school.schoolId, leadIds: school.leads, values: { status: 'new' } });
          }
          break;

        case 'attempting-contact':
        case 'working':
        case 'matched':
          console.log(`STATUS: new, sub_status: ${newValue}`);
          for (const lead of leadIds) {
            if (!lead.lead_assignment_id) {
              await _createAssignLead({
                assignedUserId: user_id,
                leadId: lead.id,
                subStatus: newValue,
                assignmentType: 'school_assigned',
              });
            } else {
              await _updateSubStatus({
                user_id,
                assignment_id: lead.lead_assignment_id,
                sub_status: newValue,
              });
            }
          }

          for (const school of leadsGroupedBySchool) {
            await updateLead({ schoolId: school.schoolId, leadIds: school.leads, values: { status: 'new' } });
          }
          break;

        default:
          console.log(`STATUS: ${newValue}, sub_status: empty`);
          for (const lead of leadIds) {
            if (lead.lead_assignment_id) {
              await _updateSubStatus({
                user_id,
                assignment_id: lead.lead_assignment_id,
                sub_status: '',
              });
            }
          }

          // update lead
          for (const school of leadsGroupedBySchool) {
            let payload = { status: newValue };
            if (newValue === 'archived' && archivedReason) {
              payload.reason_id = archivedReason;
            }
            if (newValue === 'accepted' && acceptedYear) {
              payload.year_accepted = acceptedYear;
            }

            await updateLead({ schoolId: school.schoolId, leadIds: school.leads, values: payload });
          }
          break;
      }

      return true;
    } catch (err) {
      setError(err);
      return false;
    } finally {
      await fetchLeadAssignments();
      await getStatusCounts();
      setLoading(false);
    }
  };

  const handleSentApp = async ({ schoolId, leadId, userId, sendToEmail }) => {
    setLoading(true);
    setError(null);

    try {
      const res = await _sendApplications(schoolId, [leadId], sendToEmail, userId);
      const data = await res.json();
      if (data.length > 0 && sendToEmail) {
        await fetchLeadAssignments();
        await getStatusCounts();
      }
      return data[0];
    } catch (error) {
      console.log('handleSentApp', error);
      setError(error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    data,
    statusCounts,
    loading,
    error,
    getLeadAssignments: fetchLeadAssignments,
    updateAssignment,
    handleSentApp,
    handlePageChange,
    handlePageSizeChange,
    currentPage,
    pageSize,
  };
};

const getGroupedLeads = (leadIds) => {
  const leadsFromSchoolMap = {};
  for (const lead of leadIds) {
    if (!leadsFromSchoolMap[lead.school_id]) {
      leadsFromSchoolMap[lead.school_id] = [];
    }
    leadsFromSchoolMap[lead.school_id].push(lead.id);
  }
  return Object.entries(leadsFromSchoolMap).map(([schoolId, leads]) => ({
    schoolId: Number(schoolId),
    leads,
  }));
};

function getInitialFilters() {
  const FILTERS_KEY = 'assignmentsSearch';
  try {
    const stored = localStorage.getItem(FILTERS_KEY);
    const result = stored ? JSON.parse(stored) : {};
    return result;
  } catch (error) {
    return {};
  }
}
