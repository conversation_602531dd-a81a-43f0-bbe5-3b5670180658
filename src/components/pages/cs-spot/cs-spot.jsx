import React from 'react';
import { Tabs } from 'antd';
import { InternalBriefs } from './views/internal-briefs/internal-briefs';
import styles from './cs-spot.module.scss';

const CSSpot = () => {
  const tabItems = [
    {
      key: 'internal',
      label: (
        <span
          style={{
            width: '100%',
            display: 'inline-block',
            textAlign: 'center',
          }}>
          Internal Briefs
        </span>
      ),
      children: <InternalBriefs />,
    },
    {
      key: 'family',
      label: (
        <span
          style={{
            width: '100%',
            display: 'inline-block',
            textAlign: 'center',
          }}>
          Family Retention & Experience Audits
        </span>
      ),
      children: <div />,
    },
  ];

  return (
    <div className={styles.container}>
      <h1 className="heading-1 spacing-my-16">Schola Platform</h1>
      <div style={{ width: '100%' }}>
        <Tabs
          items={tabItems}
          tabBarStyle={{
            display: 'flex',
            width: '100%',
          }}
          moreIcon={null}
          style={{ width: '100%' }}
        />
      </div>
    </div>
  );
};

export default CSSpot;
