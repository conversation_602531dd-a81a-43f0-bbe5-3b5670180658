import React, { useState } from 'react';
import { Modal, Input, Select, Button, Tooltip, Upload } from 'antd';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import {
  LinkOutlined,
  UploadOutlined,
  QuestionCircleOutlined,
  DownOutlined,
  CalendarOutlined,
  SoundOutlined,
  SyncOutlined,
  ClockCircleOutlined,
  TeamOutlined,
  LineChartOutlined,
  CodeOutlined,
  CustomerServiceOutlined,
} from '@ant-design/icons';
import PropTypes from 'prop-types';
import styles from './modal-brief.module.scss';

const { Option } = Select;

/**
 * CreateBriefModal - Modal component for creating new internal briefs
 *
 * @param {Object} props - Component properties
 * @param {boolean} props.open - Whether the modal is visible
 * @param {function} props.onClose - Handler to close the modal
 * @param {function} props.onSubmit - Handler for form submission
 */
const ModalBrief = ({ open, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    briefType: 'event',
    teamAssignment: 'cp-spot',
    tags: [],
    links: [],
    attachments: [],
  });

  const [linkInput, setLinkInput] = useState('');

  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAddLink = () => {
    const trimmed = linkInput.trim();
    if (!trimmed) return;
    if (formData.links.includes(trimmed)) return;
    handleInputChange('links', [...formData.links, trimmed]);
    setLinkInput('');
  };

  const handleRemoveLink = (link) => {
    handleInputChange(
      'links',
      formData.links.filter((l) => l !== link)
    );
  };

  // Opciones de tags (puedes ajustar según tus necesidades)
  const tagOptions = [
    { id: 'urgent', name: '#urgent' },
    { id: 'deadline', name: '#deadline' },
    { id: 'open-house', name: '#open-house' },
    { id: 'admissions', name: '#admissions' },
    { id: 'financial-aid', name: '#financial-aid' },
    { id: 'event', name: '#event' },
    { id: 'reminder', name: '#reminder' },
    { id: 'announcement', name: '#announcement' },
    // ...agrega más si es necesario
  ];

  const handleSubmit = () => {
    if (onSubmit) {
      onSubmit(formData);
    }
    handleCancel();
  };

  const handleCancel = () => {
    setFormData({
      title: '',
      content: '',
      briefType: 'event',
      teamAssignment: 'cp-spot',
      tags: [],
      links: [],
      attachments: [],
    });
    setShowAdvanced(false);
    onClose();
  };

  const briefTypeOptions = [
    { value: 'event', label: 'Event', icon: <CalendarOutlined /> },
    { value: 'announcement', label: 'Announcement', icon: <SoundOutlined /> },
    { value: 'update', label: 'Update', icon: <SyncOutlined /> },
    { value: 'reminder', label: 'Reminder', icon: <ClockCircleOutlined /> },
  ];

  const teamOptions = [
    { value: 'cp-spot', label: 'CP & SPOT', icon: <TeamOutlined /> },
    { value: 'marketing', label: 'Marketing', icon: <LineChartOutlined /> },
    { value: 'development', label: 'Development', icon: <CodeOutlined /> },
    { value: 'support', label: 'Support', icon: <CustomerServiceOutlined /> },
  ];

  const uploadProps = {
    name: 'file',
    multiple: true,
    onChange(info) {
      const { fileList } = info;
      handleInputChange('attachments', fileList);
    },
    beforeUpload: () => false, // Prevent automatic upload
  };

  const quillModules = {
    toolbar: [
      [{ header: [1, 2, false] }],
      ['bold', 'italic', 'underline'],
      [{ list: 'ordered' }, { list: 'bullet' }],
      ['link'],
      ['clean'],
    ],
  };

  const quillFormats = ['header', 'bold', 'italic', 'underline', 'list', 'bullet', 'link'];

  return (
    <Modal
      title={
        <div>
          <div className={styles.modalTitle}>Create New Internal Brief</div>
          <div className={styles.subtitle}>Share important information with your team.</div>
        </div>
      }
      open={open}
      onCancel={handleCancel}
      width={700}
      className={styles.briefModal}
      footer={[
        <Button key="cancel" onClick={handleCancel} size="large">
          Cancel
        </Button>,
        <Button key="submit" type="primary" onClick={handleSubmit} size="large">
          Create Brief
        </Button>,
      ]}
      centered>
      <div className={`${styles.modalContent} spacing-mt-16`}>
        {/* Brief Title */}
        <div className={styles.formGroup}>
          <label className={styles.label}>
            Brief Title <span className={styles.required}>*</span>
          </label>
          <Input
            placeholder="Enter a clear, descriptive title..."
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            size="large"
            className={styles.input}
          />
        </div>

        {/* Content */}
        <div className={styles.formGroup}>
          <label className={styles.label}>
            Content <span className={styles.required}>*</span>
          </label>
          <ReactQuill
            theme="snow"
            value={formData.content}
            onChange={(value) => handleInputChange('content', value)}
            modules={quillModules}
            formats={quillFormats}
            className={styles.textArea}
            placeholder="Write your brief content here..."
          />
        </div>

        {/* Brief Type */}
        <div className={styles.formGroup}>
          <label className={styles.label}>
            Brief Type
            <Tooltip title="Select the type of brief you're creating">
              <QuestionCircleOutlined className={styles.helpIcon} />
            </Tooltip>
          </label>
          <Select
            value={formData.briefType}
            onChange={(value) => handleInputChange('briefType', value)}
            size="large"
            className={styles.select}
            suffixIcon={<DownOutlined />}>
            {briefTypeOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                <span className={styles.optionContent}>
                  <span className={styles.optionIcon}>{option.icon}</span>
                  {option.label}
                </span>
              </Option>
            ))}
          </Select>
        </div>

        {/* Team Assignment */}
        <div className={styles.formGroup}>
          <label className={styles.label}>
            Team Assignment
            <Tooltip title="Select which team this brief is for">
              <QuestionCircleOutlined className={styles.helpIcon} />
            </Tooltip>
          </label>
          <Select
            value={formData.teamAssignment}
            onChange={(value) => handleInputChange('teamAssignment', value)}
            size="large"
            className={styles.select}
            suffixIcon={<DownOutlined />}>
            {teamOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                <span className={styles.optionContent}>
                  <span className={styles.optionIcon}>{option.icon}</span>
                  {option.label}
                </span>
              </Option>
            ))}
          </Select>
        </div>

        {/* Tags */}
        <div className={styles.formGroup}>
          <label className={styles.label}>Tags</label>
          <div className={styles.selectedTags} style={{ marginBottom: 8, display: 'flex', gap: 8, flexWrap: 'wrap' }}>
            {formData.tags.map((tagId) => {
              const tagObj = tagOptions.find((t) => t.id === tagId);
              return tagObj ? (
                <span
                  key={tagId}
                  className={styles.tag}
                  style={{
                    backgroundColor: '#5BA3F6',
                    color: 'white',
                    fontSize: '14px',
                    padding: '4px 12px',
                    borderRadius: '20px',
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: '8px',
                  }}>
                  {tagObj.name}
                  <button
                    type="button"
                    onClick={() => {
                      const newTags = formData.tags.filter((id) => id !== tagId);
                      handleInputChange('tags', newTags);
                    }}
                    style={{
                      background: 'none',
                      border: 'none',
                      color: 'white',
                      cursor: 'pointer',
                      fontSize: '16px',
                      lineHeight: 1,
                      padding: 0,
                    }}>
                    ×
                  </button>
                </span>
              ) : null;
            })}
          </div>
          <select
            value=""
            onChange={(e) => {
              const value = e.target.value;
              if (value && !formData.tags.includes(value)) {
                const newTags = [...formData.tags, value];
                handleInputChange('tags', newTags);
              }
              e.target.value = '';
            }}
            className={styles.selectNative}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: '1px solid #d1d5db',
              borderRadius: '8px',
              fontSize: '14px',
              backgroundColor: 'white',
            }}>
            <option value="">Add relevant tags...</option>
            {tagOptions
              .filter((t) => !formData.tags.includes(t.id))
              .map((option) => (
                <option key={option.id} value={option.id}>
                  {option.name}
                </option>
              ))}
          </select>
        </div>

        {/* Attachments */}
        <div className={styles.formGroup}>
          <label className={styles.label}>Attachments</label>
          <Upload {...uploadProps} className={styles.upload}>
            <Button icon={<UploadOutlined />} size="large" className={styles.uploadButton}>
              Upload Files
            </Button>
          </Upload>
        </div>

        {/* Links */}
        <div className={styles.formGroup}>
          <label className={styles.label}>Links</label>
          <div style={{ display: 'flex', gap: 8 }}>
            <Input
              placeholder="https://example.com"
              value={linkInput}
              onChange={(e) => setLinkInput(e.target.value)}
              size="large"
              className={styles.input}
              onPressEnter={handleAddLink}
            />
            <Button
              icon={<LinkOutlined />}
              size="large"
              onClick={handleAddLink}
              style={{ minWidth: 40 }}
              type="default"
              aria-label="Add link"
            />
          </div>
          <div className={styles.linksList}>
            {formData.links.map((link, idx) => (
              <div key={link + idx} className={styles.linkItem}>
                <LinkOutlined className={styles.linkItemIcon} />
                <a
                  href={link.startsWith('http') ? link : `https://${link}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={styles.linkItemAnchor}>
                  {link.replace(/^https?:\/\//, '')}
                </a>
                <Button
                  type="text"
                  size="small"
                  onClick={() => handleRemoveLink(link)}
                  className={styles.removeLinkButton}
                  aria-label="Remove link">
                  ×
                </Button>
              </div>
            ))}
          </div>
        </div>

        {/* Advanced Options */}
        <div className={styles.advancedSection}>
          <Button
            type="text"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className={styles.advancedToggle}
            icon={<DownOutlined className={showAdvanced ? styles.rotated : ''} />}>
            Advanced Options
          </Button>

          {showAdvanced && (
            <div className={styles.advancedContent}>
              <p className={styles.advancedText}>Additional configuration options will be available here.</p>
            </div>
          )}
        </div>
      </div>
    </Modal>
  );
};

ModalBrief.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSubmit: PropTypes.func,
};

ModalBrief.defaultProps = {
  onSubmit: null,
};

export default ModalBrief;
