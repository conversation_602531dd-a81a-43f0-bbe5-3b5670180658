.cardsRow {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  @media (max-width: 900px) {
    grid-template-columns: repeat(2, 1fr);
  }
  @media (max-width: 600px) {
    grid-template-columns: 1fr;
  }
}
.cardItem {
  width: 100%;
}
.cardIcon {
  font-size: 24px;
}

.cardNumber {
  font-size: 32px;
  font-weight: 600;
}

.cardTitle {
  font-weight: 500;
}

.cardSubtitle {
  color: #888;
}

@mixin iconContainer {
  padding: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.iconBlueContainer {
  @include iconContainer;
  border: 1px solid #bfdbfe;
  background: #e6f7ff;
}

.iconBlue {
  color: #2b6ef7;
}
.iconGreenContainer {
  @include iconContainer;
  border: 1px solid #bbf7d0;
  background: #f0fdf4;
}
.iconGreen {
  color: #34c759;
}
.iconYellowContainer {
  @include iconContainer;
  border: 1px solid #fde68a;
  background: #fffbeb;
}
.iconYellow {
  color: #f2994a;
}

.iconRedContainer {
  @include iconContainer;
  border: 1px solid #fecaca;
  background: #fef2f2;
}
.iconRed {
  color: #e53935;
}

.briefsSection {
  margin-top: 32px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 1px 4px 0 rgba(0, 0, 0, 0.03);
  border: 1px solid #eceef1;
  padding: 24px;
}

.briefsSearchBar {
  margin-bottom: 16px;
}

.searchInput {
  flex: 1;
  padding: 12px 20px;
  border: 1px solid #eceef1;
  border-radius: 8px;
  font-size: 16px;
  background: #f8f9fb;
  outline: none;
}

.filterBtn {
  background: #fff;
  border: 1px solid #eceef1;
  border-radius: 8px;
  padding: 8px 18px;
  font-size: 15px;
  color: #222;
  cursor: pointer;
}

.newBriefBtn {
  background: #e53935;
  border: none;
  border-radius: 8px;
  padding: 8px 18px;
  font-size: 15px;
  color: #fff;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
}

.briefsEmptyState {
  background: #f8f9fb;
  border-radius: 8px;
  padding: 32px 0;
  text-align: center;
  margin-top: 8px;
}

.emptyText {
  color: #8a94a6;
  font-size: 18px;
  margin-bottom: 18px;
}

.createBriefBtn {
  margin-top: 8px;
}
