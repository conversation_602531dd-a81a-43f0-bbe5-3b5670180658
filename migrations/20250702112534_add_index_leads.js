exports.up = function (knex) {
  return Promise.all([
    knex.raw(`
            CREATE INDEX idx_leads_on_normalized_contact_info ON leads (trim(lower(parent_first_name)), trim(lower(parent_last_name)), trim(lower(email)));
        `),
    knex.raw(`
            CREATE INDEX idx_leads_active_ordered_by_id ON leads (id DESC) WHERE COALESCE(deleted, false) = false;
        `),
  ]);
};

exports.down = function (knex) {
  return Promise.all([
    knex.raw(`
            DROP INDEX IF EXISTS idx_leads_on_normalized_contact_info;
        `),
    knex.raw(`
            DROP INDEX IF EXISTS idx_leads_active_ordered_by_id;
        `),
  ]);
};
