exports.up = function (knex) {
  return Promise.all([
    knex.schema.table("tracking_events", (table) => {
      table.index(["event_name"]);
      table.index(["event_type"]);
      table.index(["event_name", "event_type"]);
    }),
  ]);
};

exports.down = function (knex) {
  return Promise.all([
    knex.schema.table("tracking_events", (table) => {
      table.dropIndex(["event_name"]);
      table.dropIndex(["event_type"]);
      table.dropIndex(["event_name", "event_type"]);
    }),
  ]);
};
